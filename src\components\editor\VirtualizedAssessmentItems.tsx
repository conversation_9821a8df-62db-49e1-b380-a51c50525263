import React from 'react'
import { motion } from 'framer-motion'
import { ArrowUp, ArrowDown, GripVertical, Edit, Trash2 } from 'lucide-react'
import { Question, TextBlock } from '../../types/database'
import { AssessmentItem } from '../../types/assessment'

interface VirtualizedAssessmentItemsProps {
  selectedItems: AssessmentItem[]
  moveItemUp: (index: number) => void
  moveItemDown: (index: number) => void
  removeItem: (index: number) => void
  handleEditTextBlock: (index: number, item: TextBlock) => void
  handleViewQuestionDetail: (question: Question) => void
}

const VirtualizedAssessmentItems: React.FC<VirtualizedAssessmentItemsProps> = ({
  selectedItems,
  moveItemUp,
  moveItemDown,
  removeItem,
  handleEditTextBlock,
  handleViewQuestionDetail,
}) => {
  if (selectedItems.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Nenhum item adicionado
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Adicione questões ou blocos de texto para começar a criar sua avaliação
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="p-4 lg:p-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
          Itens da Avaliação ({selectedItems.length})
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Use as setas para reordenar os itens
        </p>
      </div>

      <div className="flex-1 overflow-auto p-4 lg:p-6">
        <div className="space-y-4">
          {selectedItems.map((item, index) => {
            return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4"
              >
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col space-y-1 flex-shrink-0">
                    <button
                      onClick={() => moveItemUp(index)}
                      disabled={index === 0}
                      className="p-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
                      aria-label="Mover para cima"
                    >
                      <ArrowUp className="w-4 h-4" />
                    </button>
                    <GripVertical className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    <button
                      onClick={() => moveItemDown(index)}
                      disabled={index === selectedItems.length - 1}
                      className="p-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
                      aria-label="Mover para baixo"
                    >
                      <ArrowDown className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="flex-1 min-w-0">
                    {('type' in item && (item as any).type === 'text') ? (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs px-2 py-1 rounded-full font-medium">
                              Bloco de Texto
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {(item as any).style === 'heading' ? 'Título' :
                               (item as any).style === 'subheading' ? 'Subtítulo' :
                               (item as any).style === 'instruction' ? 'Instrução' : 'Texto Normal'}
                            </span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => {
                                handleEditTextBlock(index, item as TextBlock)
                              }}
                              className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded transition-colors"
                              aria-label="Editar bloco de texto"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => removeItem(index)}
                              className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded transition-colors"
                              aria-label="Remover item"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        <div className={`text-gray-900 dark:text-white ${
                          (item as any).style === 'heading' ? 'text-lg font-bold' :
                          (item as any).style === 'subheading' ? 'text-base font-semibold' :
                          (item as any).style === 'instruction' ? 'text-sm italic' : 'text-sm'
                        }`}>
                          {(item as any).content}
                        </div>
                      </div>
                    ) : ('tipo' in item) ? (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full font-medium">
                              Questão
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {(item as any).disciplina} • {(item as any).serie}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                              ((item as any).dificuldade === 'facil' || (item as any).dificuldade === 'Fácil') ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                              ((item as any).dificuldade === 'medio' || (item as any).dificuldade === 'Médio') ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                            }`}>
                              {((item as any).dificuldade === 'facil' || (item as any).dificuldade === 'Fácil') ? 'Fácil' :
                               ((item as any).dificuldade === 'medio' || (item as any).dificuldade === 'Médio') ? 'Médio' : 'Difícil'}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                              (item as any).tipo === 'multipla_escolha' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' :
                              (item as any).tipo === 'dissertativa' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                              'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                            }`}>
                              {(item as any).tipo === 'multipla_escolha' ? 'Múltipla Escolha' :
                               (item as any).tipo === 'dissertativa' ? 'Dissertativa' : 'V/F'}
                            </span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => handleViewQuestionDetail(item as Question)}
                              className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded transition-colors"
                              aria-label="Ver detalhes da questão"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => removeItem(index)}
                              className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded transition-colors"
                              aria-label="Remover questão"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        <div className="text-gray-900 dark:text-white text-sm mb-2 overflow-hidden" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {(item as any).enunciado}
                        </div>

                        {(item as any).tipo === 'multipla_escolha' && ((item as any).opcoes || (item as any).alternativas) && (
                          <div className="space-y-1">
                            {((item as any).opcoes || (item as any).alternativas).slice(0, 2).map((opcao: string, idx: number) => (
                              <div key={idx} className="text-xs text-gray-600 dark:text-gray-400 truncate">
                                {String.fromCharCode(97 + idx)}) {opcao}
                              </div>
                            ))}
                            {((item as any).opcoes || (item as any).alternativas).length > 2 && (
                              <div className="text-xs text-gray-400 dark:text-gray-500">
                                ... e mais {((item as any).opcoes || (item as any).alternativas).length - 2} alternativas
                              </div>
                            )}
                          </div>
                        )}

                        {(item as any).tipo === 'dissertativa' && (item as any).metadata?.linhas_resposta && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {(item as any).metadata.linhas_resposta} linhas para resposta
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <div className="text-yellow-800 dark:text-yellow-200 text-sm font-medium">
                          Item não identificado
                        </div>
                        <div className="text-yellow-600 dark:text-yellow-400 text-xs mt-1">
                          Tipo: {JSON.stringify(Object.keys(item))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default VirtualizedAssessmentItems
