import React, { useState, useMemo, useCallback } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Check, Star, Zap, Building } from 'lucide-react'
import { usePlans } from '../../hooks/usePlans'

const PricingSection: React.FC = () => {
  const { plans, isLoading: plansLoading } = usePlans({ isActive: true })
  const [isAnnual, setIsAnnual] = useState(false)

  // Memoize filtered and ordered plans to prevent infinite re-renders
  const orderedPlans = useMemo(() => {
    if (!plans) return []

    // Filter plans by duration
    const filteredPlans = plans.filter(plan =>
      isAnnual ? plan.duration_months === 12 : plan.duration_months === 1
    )



    // Define the desired order for plan display
    const planOrder = ['Gratuito', 'Premium', 'Escolar']

    // Sort plans according to the desired order
    const sorted = filteredPlans.sort((a, b) => {
      const aBaseName = a.name.replace(' Anual', '')
      const bBaseName = b.name.replace(' Anual', '')
      const aIndex = planOrder.indexOf(aBaseName)
      const bIndex = planOrder.indexOf(bBaseName)

      // If plan is not in the order array, put it at the end
      if (aIndex === -1) return 1
      if (bIndex === -1) return -1

      return aIndex - bIndex
    })



    return sorted
  }, [plans, isAnnual])

  // Memoize calculate savings function
  const calculateSavings = useCallback((annualPlan: any) => {
    if (!plans || annualPlan.duration_months !== 12) return null

    const baseName = annualPlan.name.replace(' Anual', '')
    const monthlyPlan = plans.find(p => p.name === baseName && p.duration_months === 1)

    if (!monthlyPlan) return null

    const monthlyTotal = parseFloat(monthlyPlan.price) * 12
    const annualPrice = parseFloat(annualPlan.price)
    const savings = monthlyTotal - annualPrice
    const percentage = Math.round((savings / monthlyTotal) * 100)

    return { savings, percentage, monthlyTotal }
  }, [plans])

  if (plansLoading) {
    return (
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Planos para cada necessidade
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comece gratuitamente e faça upgrade quando precisar de mais recursos
            </p>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      </section>
    )
  }

  // Debug: verificar se chegou até aqui
  if (!plans || orderedPlans.length === 0) {
    return (
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Planos para cada necessidade
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comece gratuitamente e faça upgrade quando precisar de mais recursos
            </p>
          </div>
          <div className="flex items-center justify-center py-12">
            <p className="text-gray-600 dark:text-gray-400">Nenhum plano encontrado</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Planos para cada necessidade
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Comece gratuitamente e faça upgrade quando precisar de mais recursos
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <span className={`text-sm font-medium ${!isAnnual ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}`}>
              Mensal
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isAnnual ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${isAnnual ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}`}>
              Anual
            </span>
            {isAnnual && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Economize até 25%
              </span>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {orderedPlans.map((plan, index) => {
            const baseName = plan.name.replace(' Anual', '')
            const isPopular = baseName === 'Premium' // Premium é o mais popular
            const savings = calculateSavings(plan)

            return (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl dark:border-gray-700 dark:hover:border-gray-600 ${
                  isPopular
                    ? 'border-blue-500 scale-105'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                      <Star className="w-4 h-4" />
                      <span>Mais Popular</span>
                    </div>
                  </div>
                )}

                <div className="p-8">
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 ${
                      baseName === 'Gratuito' ? 'bg-gray-100 dark:bg-gray-700' :
                      baseName === 'Premium' ? 'bg-blue-100 dark:bg-blue-900' :
                      'bg-purple-100 dark:bg-purple-900'
                    }`}>
                      {baseName === 'Gratuito' && <Zap className="w-8 h-8 text-gray-600 dark:text-gray-400" />}
                      {baseName === 'Premium' && <Star className="w-8 h-8 text-blue-600 dark:text-blue-400" />}
                      {baseName === 'Escolar' && <Building className="w-8 h-8 text-purple-600 dark:text-purple-400" />}
                    </div>

                    <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-50 mb-2">
                      {baseName}
                      {isAnnual && (
                        <span className="ml-2 text-sm font-normal text-green-600 dark:text-green-400">
                          Anual
                        </span>
                      )}
                    </h3>

                    <div className="mb-4">
                      {isAnnual && savings ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-center space-x-2">
                            <span className="text-2xl font-bold text-gray-900 dark:text-white">
                              R$ {parseFloat(plan.price).toFixed(2).replace('.', ',')}
                            </span>
                            <span className="text-gray-600 dark:text-gray-400">/ano</span>
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            <span className="line-through">
                              R$ {savings.monthlyTotal.toFixed(2).replace('.', ',')}
                            </span>
                            <span className="ml-2 text-green-600 dark:text-green-400 font-medium">
                              Economize {savings.percentage}%
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            R$ {(parseFloat(plan.price) / 12).toFixed(2).replace('.', ',')}/mês
                          </div>
                        </div>
                      ) : (
                        <div>
                          <span className="text-4xl font-bold text-gray-900 dark:text-white">
                            R$ {parseFloat(plan.price).toFixed(2).replace('.', ',')}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            /{isAnnual ? 'ano' : 'mês'}
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-gray-600 dark:text-gray-300">{plan.description}</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features?.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start space-x-3">
                        <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 dark:text-gray-200">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    to={baseName === "Escolar" ? "/contact" : "/register"}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-colors block text-center ${
                      isPopular
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : baseName === 'Escolar'
                          ? 'bg-purple-600 hover:bg-purple-700 text-white'
                          : 'bg-gray-900 hover:bg-gray-800 text-white'
                    }`}
                  >
                    {baseName === "Gratuito" ? "Começar Agora" :
                     baseName === "Escolar" ? "Falar com Consultor" :
                     `Assinar ${baseName}`}
                  </Link>
                </div>
              </motion.div>
            )
          })}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Todas as assinaturas incluem 7 dias de teste gratuito
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Cancele a qualquer momento. Sem taxas de cancelamento.
          </p>
        </div>
      </div>
    </section>
  )
}

export default PricingSection