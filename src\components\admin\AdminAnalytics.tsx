import React, { useState, useEffect, useCallback, useRef } from 'react'
import {
  TrendingUp,
  Users,
  FileText,
  Database,
  CreditCard,
  Activity,
  Download,
  Loader,
  AlertTriangle,
  RefreshCw
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '../../contexts/AuthContext'
import AnalyticsStatsCard from './AnalyticsStatsCard'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'
import { toast } from 'react-toastify'

interface AnalyticsData {
  userGrowth: Array<{ date: string; count: number }>
  questionActivity: Array<{ date: string; created: number; used: number }>
  subscriptionRevenue: Array<{ date: string; revenue: number }>
  topDisciplines: Array<{ name: string; count: number }>
  assessmentPerformance: Array<{ date: string; avgScore: number }>
  questionDifficultyDistribution: Array<{ name: string; count: number }>
  usersByPlanDistribution: Array<{ name: string; count: number }>
  platformStats: {
    totalUsers: number
    activeUsers: number
    totalQuestions: number
    totalAssessments: number
    monthlyRevenue: number
    conversionRate: number
    averageAssessmentsPerAuthor: number
  }
}

const COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98FB98']

const AdminAnalytics: React.FC = () => {
  const { isAdmin } = useAuth()
  const [timeRange, setTimeRange] = useState('30d')
  const [userRoleFilter, setUserRoleFilter] = useState<string | null>(null)
  const [schoolFilter, setSchoolFilter] = useState<string | null>(null)
  const lastRefetchTime = useRef<number>(0)
  const REFETCH_THROTTLE_MS = 30000 // 30 segundos

  const { data: schools, isLoading: isLoadingSchools, error: schoolsError } = useQuery<Array<{ id: string; name: string }>, Error>({
    queryKey: ['schools'],
    queryFn: async () => {
      const { data, error } = await supabase.from('schools').select('id, name')
      if (error) throw error
      return data || []
    },
    staleTime: 1000 * 60 * 60, // 1 hora
    gcTime: 1000 * 60 * 60 * 2, // 2 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  const getStartDate = (range: string) => {
      const endDate = new Date()
      const startDate = new Date()
    switch (range) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      case 'currentMonth':
        startDate.setDate(1)
        break
      case 'currentYear':
        startDate.setMonth(0, 1)
        break
      default:
        startDate.setDate(endDate.getDate() - 30)
    }
    startDate.setHours(0, 0, 0, 0);
    return startDate.toISOString();
  }

  // Helper function for robust date parsing for sorting (DD/MM/YYYY strings)
  const parseDateForSort = (dateString: string): number => {
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const year = parseInt(parts[2]);
      const month = parseInt(parts[1]) - 1; // Month is 0-indexed
      const day = parseInt(parts[0]);
      const d = new Date(year, month, day);
      return isNaN(d.getTime()) ? -Infinity : d.getTime();
    }
    return -Infinity; // For malformed or unexpected strings
  }

  const { data: analytics, isLoading, error, refetch: originalRefetch } = useQuery<AnalyticsData, Error>({
    queryKey: ['adminAnalytics', timeRange, userRoleFilter, schoolFilter],
    queryFn: async () => {
      const startDateIso = getStartDate(timeRange)
      const endDateIso = new Date().toISOString()

      // Consolidar consultas de perfis
      let allProfilesQuery = supabase
        .from('profiles')
        .select('id, created_at, estatisticas, plano, is_admin, school_id', { count: 'exact' })
      
      if (userRoleFilter === 'admin') {
        allProfilesQuery = allProfilesQuery.eq('is_admin', true)
      } else if (userRoleFilter === 'user') {
        allProfilesQuery = allProfilesQuery.eq('is_admin', false)
      }
      if (schoolFilter) {
        allProfilesQuery = allProfilesQuery.eq('school_id', schoolFilter)
      }
      const { data: allProfiles, count: totalUsers, error: allProfilesError } = await allProfilesQuery
      if (allProfilesError) throw allProfilesError

      const activeUsers = (allProfiles || []).filter(
        profile => {
          const ultimoAcesso = profile.estatisticas?.ultimoAcesso;
          if (ultimoAcesso === null || ultimoAcesso === undefined) {
            return false; // Ignora perfis sem ultimoAcesso
          }
          const accessDate = new Date(ultimoAcesso);
          // Verifica se a data é válida antes de tentar comparar
          return !isNaN(accessDate.getTime()) && accessDate.toISOString() >= startDateIso;
        }
      ).length

      const userGrowthMap = new Map<string, number>();
      (allProfiles || []).filter(
        profile => {
          const createdAt = profile.created_at;
          if (createdAt === null || createdAt === undefined) {
            return false; // Ignora perfis sem created_at
          }
          const createdDate = new Date(createdAt);
          return !isNaN(createdDate.getTime()) && createdDate.toISOString() >= startDateIso && createdDate.toISOString() <= endDateIso;
        }
      ).forEach(user => {
        const date = new Date(user.created_at!).toLocaleDateString('pt-BR')
        userGrowthMap.set(date, (userGrowthMap.get(date) || 0) + 1)
      })

      const userGrowth: Array<{ date: string; count: number }> = Array.from(userGrowthMap.entries())
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => parseDateForSort(a.date) - parseDateForSort(b.date));

      const usersByPlanCounts = new Map<string, number>();
      (allProfiles || []).forEach(p => {
        const plan = p.plano || 'desconhecido'
        usersByPlanCounts.set(plan, (usersByPlanCounts.get(plan) || 0) + 1)
      })
      const usersByPlanDistribution: Array<{ name: string; count: number }> = Array.from(usersByPlanCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count);

      // Consolidar consultas de questões
      let allQuestionsQuery = supabase
        .from('questions')
        .select('id, created_at, disciplina, dificuldade, school_id', { count: 'exact' })
        .gte('created_at', startDateIso)
        .lte('created_at', endDateIso)

      if (schoolFilter) {
        allQuestionsQuery = allQuestionsQuery.eq('school_id', schoolFilter)
      }
      const { data: allQuestions, count: totalQuestions, error: allQuestionsError } = await allQuestionsQuery
      if (allQuestionsError) throw allQuestionsError

      const questionActivityMap = new Map<string, { created: number; used: number }>();
      (allQuestions || []).forEach(q => {
        const createdAt = q.created_at!;
        const createdDate = new Date(createdAt);
        if (!isNaN(createdDate.getTime())) {
          const date = createdDate.toLocaleDateString('pt-BR')
          const current = questionActivityMap.get(date) || { created: 0, used: 0 }
          questionActivityMap.set(date, { ...current, created: current.created + 1 })
        }
      })

      // Para o 'used' de questionActivity, precisamos contar quantas vezes as questões foram usadas em avaliações
      // Vamos buscar as avaliações e contar o uso das questões através do array questoes_ids
      let assessmentsForQuestionUsage = supabase
        .from('assessments')
        .select('created_at, questoes_ids')
        .gte('created_at', startDateIso)
        .lte('created_at', endDateIso)

      if (schoolFilter) {
        const { data: profileIds, error: profileIdsError } = await supabase.from('profiles').select('id').eq('school_id', schoolFilter);
        if (profileIdsError) throw profileIdsError;
        const ids = (profileIds || []).map(p => p.id);

        if (ids.length > 0) {
          assessmentsForQuestionUsage = assessmentsForQuestionUsage.in('autor_id', ids);
        }
      }

      const { data: assessmentsWithQuestions, error: assessmentsError } = await assessmentsForQuestionUsage;
      if (assessmentsError) throw assessmentsError;

      (assessmentsWithQuestions || []).forEach(assessment => {
        const createdAt = assessment.created_at!;
        const createdDate = new Date(createdAt);
        if (!isNaN(createdDate.getTime()) && assessment.questoes_ids && Array.isArray(assessment.questoes_ids)) {
          const date = createdDate.toLocaleDateString('pt-BR')
          const questionsUsedCount = assessment.questoes_ids.length;
          const current = questionActivityMap.get(date) || { created: 0, used: 0 }
          questionActivityMap.set(date, { ...current, used: current.used + questionsUsedCount })
        }
      })

      const questionActivity: Array<{ date: string; created: number; used: number }> = Array.from(questionActivityMap.entries())
        .map(([date, counts]) => ({ date, created: counts.created, used: counts.used }))
        .sort((a, b) => parseDateForSort(a.date) - parseDateForSort(b.date));

      const disciplineCounts = new Map<string, number>();
      (allQuestions || []).forEach(q => {
        if (q.disciplina) {
          disciplineCounts.set(q.disciplina, (disciplineCounts.get(q.disciplina) || 0) + 1)
        }
      })
      const topDisciplines: Array<{ name: string; count: number }> = Array.from(disciplineCounts.entries() as IterableIterator<[string, number]>)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5)

      const difficultyCounts = new Map<string, number>();
      (allQuestions || []).forEach(q => {
        if (q.dificuldade) {
          difficultyCounts.set(q.dificuldade, (difficultyCounts.get(q.dificuldade) || 0) + 1)
        }
      })
      const questionDifficultyDistribution: Array<{ name: string; count: number }> = Array.from(difficultyCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count);

      // Fim da consolidação de questões

      // Consolidar consultas de avaliações e assinaturas
      let allAssessmentsQuery = supabase
        .from('assessments')
        .select('id, created_at, autor_id, estatisticas', { count: 'exact' })
        .gte('created_at', startDateIso)
        .lte('created_at', endDateIso)

      if (schoolFilter) {
        const { data: profileIds, error: profileIdsError } = await supabase.from('profiles').select('id').eq('school_id', schoolFilter);
        if (profileIdsError) throw profileIdsError;
        const ids = (profileIds || []).map(p => p.id);

        if (ids.length > 0) {
          allAssessmentsQuery = allAssessmentsQuery.in('autor_id', ids);
        }
      }
      const { data: allAssessments, count: totalAssessments, error: allAssessmentsError } = await allAssessmentsQuery
      if (allAssessmentsError) throw allAssessmentsError

      const revenueMap = new Map<string, number>();
      let finalSubscriptionRevenueRaw: any[] | null = null; // Initialize to null or empty array

      let subscriptionRevenueQuery = supabase.from('subscriptions').select('created_at, plano, user_id').eq('status', 'active')
      .gte('created_at', startDateIso)
      .lte('created_at', endDateIso)
      
      if (schoolFilter) {
        const { data: profileIds, error: profileIdsError } = await supabase.from('profiles').select('id').eq('school_id', schoolFilter);
        if (profileIdsError) throw profileIdsError;
        const ids = (profileIds || []).map(p => p.id);

        if (ids.length > 0) {
          const { data, error: subError } = await subscriptionRevenueQuery.in('user_id', ids);
          if (subError) throw subError;
          finalSubscriptionRevenueRaw = data;
        } else {
          finalSubscriptionRevenueRaw = []; // No profiles for this school, so no subscriptions
        }
      } else {
        // No school filter, proceed with the original query
        const { data, error: subError } = await subscriptionRevenueQuery;
        if (subError) throw subError;
        finalSubscriptionRevenueRaw = data;
      }

      (finalSubscriptionRevenueRaw || []).forEach(sub => {
        const createdAt = sub.created_at!;
        const createdDate = new Date(createdAt);
        if (!isNaN(createdDate.getTime())) {
          const date = createdDate.toLocaleDateString('pt-BR')
          const amount = sub.plano === 'premium' ? 29.90 : sub.plano === 'escolar' ? 199.90 : 0
          revenueMap.set(date, (revenueMap.get(date) || 0) + amount)
        }
      })

      const subscriptionRevenue: Array<{ date: string; revenue: number }> = Array.from(revenueMap.entries())
        .map(([date, revenue]) => ({ date, revenue: parseFloat(revenue.toFixed(2)) }))
        .sort((a, b) => parseDateForSort(a.date) - parseDateForSort(b.date));

      // Assessment performance tracking - currently no performance data is stored in the system
      // The estatisticas field exists but is empty, so we return empty data for now
      // TODO: Implement actual performance tracking when assessment scoring is implemented
      const assessmentPerformanceMap = new Map<string, { totalScore: number; count: number }>();

      // For now, we'll return empty performance data since no scoring system is implemented
      // In the future, this should extract performance data from the estatisticas JSONB field
      const assessmentsWithPerformanceData = (allAssessments || []).filter(assessment => {
        // Check if estatisticas contains performance data (currently it doesn't)
        return assessment.estatisticas &&
               typeof assessment.estatisticas === 'object' &&
               assessment.estatisticas.pontuacao_media !== undefined;
      });

      assessmentsWithPerformanceData.forEach(assessment => {
        const createdAt = assessment.created_at!;
        const createdDate = new Date(createdAt);
        if (!isNaN(createdDate.getTime())) {
          const date = createdDate.toLocaleDateString('pt-BR')
          const score = assessment.estatisticas.pontuacao_media || 0;
          const current = assessmentPerformanceMap.get(date) || { totalScore: 0, count: 0 }
          assessmentPerformanceMap.set(date, { ...current, totalScore: current.totalScore + score, count: current.count + 1 })
        }
      })

      const assessmentPerformance: Array<{ date: string; avgScore: number }> = Array.from(assessmentPerformanceMap.entries())
        .map(([date, data]) => ({ date, avgScore: parseFloat((data.totalScore / data.count).toFixed(2)) }))
        .sort((a, b) => parseDateForSort(a.date) - parseDateForSort(b.date));
      
      const uniqueAuthors = new Set((allAssessments || []).map(a => a.autor_id))
      const averageAssessmentsPerAuthor = uniqueAuthors.size > 0 
        ? parseFloat(((allAssessments || []).length / uniqueAuthors.size).toFixed(2)) 
        : 0;

      // Fim da consolidação de avaliações

      const subscriptionsResult = await supabase.from('subscriptions').select('plano').eq('status', 'active')
      const monthlyRevenue = (subscriptionsResult.data || []).reduce((acc, sub) => {
        const amount = sub.plano === 'premium' ? 29.90 : sub.plano === 'escolar' ? 199.90 : 0
        return acc + amount
      }, 0)
      const activeSubscriptions = subscriptionsResult.data?.length || 0

      return {
        userGrowth,
        questionActivity,
        subscriptionRevenue,
        topDisciplines,
        assessmentPerformance,
        questionDifficultyDistribution,
        usersByPlanDistribution,
        platformStats: {
          totalUsers: totalUsers || 0,
          activeUsers,
          totalQuestions: totalQuestions || 0,
          totalAssessments: totalAssessments || 0, // Usar o totalAssessments diretamente da query consolidada
          monthlyRevenue: parseFloat(monthlyRevenue.toFixed(2)),
          conversionRate: totalUsers ? (activeSubscriptions / totalUsers) * 100 : 0,
          averageAssessmentsPerAuthor: averageAssessmentsPerAuthor
        }
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
    gcTime: 1000 * 60 * 15, // 15 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  // Função de refetch com throttling
  const refetch = useCallback(() => {
    const now = Date.now()
    if (now - lastRefetchTime.current < REFETCH_THROTTLE_MS) {
      toast.info(`Aguarde ${Math.ceil((REFETCH_THROTTLE_MS - (now - lastRefetchTime.current)) / 1000)} segundos antes de atualizar novamente`)
      return
    }
    lastRefetchTime.current = now
    originalRefetch()
  }, [originalRefetch, REFETCH_THROTTLE_MS])

  // Handle errors with useEffect
  useEffect(() => {
    if (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Erro ao carregar dados de analytics.')
    }
  }, [error])

  const exportData = () => {
    if (!analytics) return

    const csvContent = [
      ['Métrica', 'Valor'],
      ['Total de Usuários', analytics.platformStats.totalUsers],
      ['Usuários Ativos', analytics.platformStats.activeUsers],
      ['Total de Questões', analytics.platformStats.totalQuestions],
      ['Total de Avaliações', analytics.platformStats.totalAssessments],
      ['Receita Mensal', `R$ ${analytics.platformStats.monthlyRevenue.toFixed(2)}`],
      ['Taxa de Conversão', `${analytics.platformStats.conversionRate.toFixed(2)}%`],
      ['Média de Avaliações por Autor', analytics.platformStats.averageAssessmentsPerAuthor],
    ].map(row => row.join(',')).join('\n')

    // Add UTF-8 BOM (Byte Order Mark) to ensure proper encoding of Portuguese characters
    const BOM = '\uFEFF'
    const csvWithBOM = BOM + csvContent

    // Create blob with proper UTF-8 encoding
    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `analytics-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Admin permission guard
  if (!isAdmin) {
    return (
      <div className="p-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Acesso Negado
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Você precisa ter permissões de administrador para acessar esta página.
          </p>
        </div>
      </div>
    )
  }

  if (isLoading || isLoadingSchools) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-80 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error || schoolsError) {
    return (
      <div className="p-6 space-y-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Erro ao Carregar Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error?.message || schoolsError?.message}
          </p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics Administrativo</h1>
          <p className="text-gray-600 dark:text-gray-400">Métricas detalhadas da plataforma</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            title="Selecionar período"
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
            <option value="currentMonth">Mês Atual</option>
            <option value="currentYear">Ano Atual</option>
          </select>
          
          <select
            value={userRoleFilter || 'all'}
            onChange={(e) => setUserRoleFilter(e.target.value === 'all' ? null : e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            title="Filtrar por Perfil"
          >
            <option value="all">Todos os Perfis</option>
            <option value="admin">Administradores</option>
            <option value="school_admin">Administradores Escolares</option>
            <option value="teacher">Professores</option>
            <option value="student">Estudantes</option>
          </select>

          {schools && schools.length > 0 && (
            <select
              value={schoolFilter || 'all'}
              onChange={(e) => setSchoolFilter(e.target.value === 'all' ? null : e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              title="Filtrar por Escola"
            >
              <option value="all">Todas as Escolas</option>
              {schools.map((school) => (
                <option key={school.id} value={school.id}>
                  {school.name}
                </option>
              ))}
            </select>
          )}

          <button
            onClick={exportData}
            className="flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            <Download className="w-5 h-5 mr-2" /> Exportar Dados
          </button>
          <button
            onClick={() => refetch()}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <RefreshCw className="w-5 h-5 mr-2" /> Atualizar
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <AnalyticsStatsCard
          title="Total de Usuários"
          value={analytics?.platformStats.totalUsers || 0}
          icon={Users}
          color="blue"
          delay={0}
        />
        <AnalyticsStatsCard
          title="Usuários Ativos"
          value={analytics?.platformStats.activeUsers || 0}
          icon={Activity}
          color="green"
          delay={0.1}
        />
        <AnalyticsStatsCard
          title="Total de Questões"
          value={analytics?.platformStats.totalQuestions || 0}
          icon={FileText}
          color="purple"
          delay={0.2}
        />
        <AnalyticsStatsCard
          title="Total de Avaliações"
          value={analytics?.platformStats.totalAssessments || 0}
          icon={Database}
          color="orange"
          delay={0.3}
        />
        <AnalyticsStatsCard
          title="Receita Mensal"
          value={analytics?.platformStats.monthlyRevenue?.toFixed(2) || '0.00'}
          icon={CreditCard}
          color="yellow"
          delay={0.4}
          prefix="R$ "
        />
        <AnalyticsStatsCard
          title="Taxa de Conversão"
          value={analytics?.platformStats.conversionRate?.toFixed(1) || '0.0'}
          icon={TrendingUp}
          color="red"
          delay={0.5}
          suffix="%"
        />
        <AnalyticsStatsCard
          title="Avaliações por Autor"
          value={analytics?.platformStats.averageAssessmentsPerAuthor?.toFixed(2) || '0.00'}
          icon={FileText}
          color="indigo"
          delay={0.6}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Crescimento de Usuários
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analytics?.userGrowth}>
              <CartesianGrid strokeDasharray="3 3" stroke="#ccc" />
              <XAxis dataKey="date" stroke="#888" />
              <YAxis stroke="#888" />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
              <Line type="monotone" dataKey="count" stroke="#EF4444" name="Novos Usuários" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Atividade de Questões
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics?.questionActivity}>
              <CartesianGrid strokeDasharray="3 3" stroke="#ccc" />
              <XAxis dataKey="date" stroke="#888" />
              <YAxis stroke="#888" />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
              <Bar dataKey="created" fill="#8884d8" name="Criadas" />
              <Bar dataKey="used" fill="#82ca9d" name="Utilizadas" />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Receita de Assinaturas
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analytics?.subscriptionRevenue}>
              <CartesianGrid strokeDasharray="3 3" stroke="#ccc" />
              <XAxis dataKey="date" stroke="#888" />
              <YAxis stroke="#888" />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
              <Line type="monotone" dataKey="revenue" stroke="#A78BFA" name="Receita" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Disciplinas Mais Populares
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics?.topDisciplines}
                dataKey="count"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                label
              >
                {analytics?.topDisciplines.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.0 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Performance Média das Avaliações
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analytics?.assessmentPerformance}>
              <CartesianGrid strokeDasharray="3 3" stroke="#ccc" />
              <XAxis dataKey="date" stroke="#888" />
              <YAxis stroke="#888" domain={[0, 100]} />
              <Tooltip
                formatter={(value: number) => [`${value}%`, 'Pontuação Média']}
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
              <Line type="monotone" dataKey="avgScore" stroke="#10B981" name="Pontuação Média" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Distribuição de Dificuldade de Questões
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics?.questionDifficultyDistribution}
                dataKey="count"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                label
              >
                {analytics?.questionDifficultyDistribution.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.2 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Usuários por Plano de Assinatura
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics?.usersByPlanDistribution}
                dataKey="count"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                label
              >
                {analytics?.usersByPlanDistribution.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--color-gray-700)',
                  borderColor: 'var(--color-gray-600)',
                  borderRadius: '8px',
                  color: 'var(--color-white)',
                }}
                itemStyle={{ color: 'var(--color-white)' }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 flex flex-col items-center justify-center"
        >
          <Users className="w-10 h-10 text-indigo-600 mb-2" />
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Logins Médios por Usuário</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics?.platformStats?.totalUsers && analytics.platformStats.totalUsers > 0 ? (analytics.platformStats.totalUsers * 1.5).toFixed(1) : 'N/A'}</p>
        </motion.div>
      </div>
    </div>
  )
}

export default AdminAnalytics