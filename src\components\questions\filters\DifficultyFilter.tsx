import React from 'react'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
}

interface DifficultyFilterProps {
  filters: QuestionFilters
  onFilterChange: (key: keyof QuestionFilters, value: string | string[] | boolean) => void
}

const DIFFICULTY_OPTIONS = [
  { value: 'Fácil', label: 'Fácil' },
  { value: 'Médio', label: '<PERSON>é<PERSON>' },
  { value: 'Difícil', label: 'Difícil' }
]

const TYPE_OPTIONS = [
  { value: 'multipla_escolha', label: 'Múl<PERSON>la Escolha' },
  { value: 'dissertativa', label: 'Dissertativa' },
  { value: 'verdadeiro_falso', label: 'Verdadeiro/Falso' }
]

const DifficultyFilter: React.FC<DifficultyFilterProps> = ({
  filters,
  onFilterChange
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      {/* Dificuldade */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Dificuldade
        </label>
        <select
          value={filters.dificuldade || ''}
          onChange={(e) => onFilterChange('dificuldade', (e.target as HTMLSelectElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por dificuldade"
          aria-label="Filtrar por dificuldade"
        >
          <option value="">Todas</option>
          {DIFFICULTY_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Tipo */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Tipo
        </label>
        <select
          value={filters.tipo || ''}
          onChange={(e) => onFilterChange('tipo', (e.target as HTMLSelectElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por tipo"
          aria-label="Filtrar por tipo"
        >
          <option value="">Todos</option>
          {TYPE_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}

export default DifficultyFilter
