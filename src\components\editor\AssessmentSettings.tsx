import React, { useState } from 'react'
import { X } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { AssessmentConfig } from '../../types/assessment'
import HeaderSettings from './settings/HeaderSettings'
import FooterSettings from './settings/FooterSettings'
import FontSettings from './settings/FontSettings'

interface AssessmentSettingsProps {
  isOpen: boolean
  config: AssessmentConfig
  onConfigChange: (config: AssessmentConfig) => void
  onClose: () => void
}

const AssessmentSettings: React.FC<AssessmentSettingsProps> = ({
  isOpen,
  config,
  onConfigChange,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('header')

  const tabs = [
    { id: 'header', label: 'Cabeçalho' },
    { id: 'footer', label: 'Rodapé' },
    { id: 'font', label: 'Fonte' }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'header':
        return <HeaderSettings config={config} onConfigChange={onConfigChange} />
      case 'footer':
        return <FooterSettings config={config} onConfigChange={onConfigChange} />
      case 'font':
        return <FontSettings config={config} onConfigChange={onConfigChange} />
      default:
        return <HeaderSettings config={config} onConfigChange={onConfigChange} />
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-5xl max-h-[85vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Configurações da Avaliação
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="flex border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-400'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            <div className="p-6">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {renderTabContent()}
              </motion.div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default AssessmentSettings