import React, { useState, useEffect, useRef } from 'react'
import {
  Save,
  Eye,
  Download,
  Settings,
  Plus,
  Trash2,
  GripVertical,
  FileText,
  Clock,
  X,
  Search,
  Filter,
  Edit,
  AlignLeft,
  Sparkles,
  ArrowUp,
  ArrowDown,
  AlertTriangle,
  Crown
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLocation, useNavigate } from 'react-router-dom'
import { useQuestions } from '../../hooks/useQuestions'
import { useAssessments } from '../../hooks/useAssessments'
import { useTemplates } from '../../hooks/useTemplates'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useUsageLimits } from '../../hooks/useUsageLimits'
import { generatePDF, downloadPDF } from '../../utils/pdfGenerator'
import { WATERMARK_CONFIG } from '../../constants/usageLimits'
import QuestionCard from '../questions/QuestionCard'
import AssessmentPreview from './AssessmentPreview'
import UsageDashboard from '../common/UsageDashboard'
import AssessmentSettings from './AssessmentSettings'
import QuestionDetailModal from './QuestionDetailModal'
import TextBlockModal from './TextBlockModal'
import CustomQuestionModal from './CustomQuestionModal'
import SaveAsTemplateModal from './SaveAsTemplateModal'
import VirtualizedAssessmentItems from './VirtualizedAssessmentItems'
import { Database } from '../../types/database'
import { CreateTemplateDTO } from '../../types/templates'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import { useQuestionSelection } from '../../contexts/QuestionSelectionContext'
import { useAuth } from '../../contexts/AuthContext'
import { AssessmentConfig, TextBlock, AssessmentItem } from '../../types/assessment'
import { useAssets } from '../../hooks/useAssets'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'

type Question = Database['public']['Tables']['questions']['Row']

const AssessmentEditor: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { templates } = useTemplates()
  const { canAccess, isPremium, isEscolar } = useSubscription()
  const { isAdmin, user } = useAuth()
  const { getAssetUrl } = useAssets()
  const {
    limitStatus,
    checkCanPerformAction,
    trackUsage,
    isPaidUser
  } = useUsageLimits()
  const [selectedItems, setSelectedItems] = useState<AssessmentItem[]>([])
  const [showPreview, setShowPreview] = useState(false)


  const [showSettings, setShowSettings] = useState(false)
  const [showQuestionBank, setShowQuestionBank] = useState(true)
  const [showQuestionDetail, setShowQuestionDetail] = useState(false)
  const [showTextBlockModal, setShowTextBlockModal] = useState(false)
  const [showCustomQuestionModal, setShowCustomQuestionModal] = useState(false)
  const [showSaveAsTemplateModal, setShowSaveAsTemplateModal] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [editingTextBlock, setEditingTextBlock] = useState<TextBlock | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [visibleCount, setVisibleCount] = useState(10)
  const [filters, setFilters] = useState({
    disciplina: '',
    serie: '',
    dificuldade: '',
    tipo: ''
  })
  
  const [assessmentConfig, setAssessmentConfig] = useState<AssessmentConfig>({
    titulo: 'Nova Avaliação',
    disciplina: '',
    serie: '',
    headerConfig: {
      nomeEscola: 'Nome da Escola',
      nomeProva: 'Avaliação',
      serie: '',
      data: new Date().toLocaleDateString('pt-BR'),
      instrucoes: 'Leia atentamente cada questão antes de responder.'
    },
    pdfOptions: {
      paperSize: 'A4',
      orientation: 'portrait',
      fontSize: 'medium',
      fontFamily: 'times',
      lineSpacing: 'normal',
      includeAnswerSheet: false,
      generateVersions: 1
    },
    showFooter: true
  })

  const { questions, isLoading } = useQuestions({
    search: searchTerm,
    ...filters,
    status: 'approved' // Apenas questões aprovadas
  })
  const { createAssessment, isCreating } = useAssessments()
  const { selectedIds, clearSelection } = useQuestionSelection()

  // Função para converter questão do banco para AssessmentItem
  const convertQuestionToAssessmentItem = (question: Question): AssessmentItem => {
    return {
      id: question.id,
      enunciado: question.enunciado,
      tipo: question.tipo,
      opcoes: question.alternativas || undefined,
      resposta_correta: question.resposta_correta,
      disciplina: question.disciplina,
      serie: question.serie,
      dificuldade: question.dificuldade === 'Fácil' ? 'facil' :
                   question.dificuldade === 'Médio' ? 'medio' : 'dificil',
      tags: question.tags || [],
      imagem_url: question.imagem_url || undefined,
      explicacao: question.explicacao,
      created_at: question.created_at,
      updated_at: question.updated_at,
      autor_id: question.autor_id || '',
      status: question.status,
      metadata: question.metadata
    }
  }

  // Ref to scroll to when adding a new text block
  const newItemRef = useRef<HTMLDivElement>(null)

  // Modal para definir nome da avaliação antes de salvar
  const [showNameModal, setShowNameModal] = useState(false)
  const [tempAssessmentTitle, setTempAssessmentTitle] = useState('')

  // Effect to load template from URL
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search)
    const templateId = queryParams.get('templateId')
    const assessmentId = queryParams.get('aid')

    if (assessmentId) {
      // Carregar avaliação existente
      const loadAssessment = async () => {
        try {
          toast.loading('Carregando avaliação...', { id: 'load-assessment' })
          
          console.log('Carregando avaliação com ID:', assessmentId)
          
          const { data: assessment, error } = await supabase
            .from('assessments')
            .select('*')
            .eq('id', assessmentId)
            .single()

          if (error) throw error
          
          console.log('Avaliação carregada:', assessment)

          // Carregar questões da avaliação
          console.log('Questões IDs:', assessment.questoes_ids)
          console.log('Configuração:', assessment.configuracao)
          
          if (assessment.questoes_ids && assessment.questoes_ids.length > 0) {
            console.log('Carregando questões do banco...')
            const { data: questionsData, error: questionsError } = await supabase
              .from('questions')
              .select('*')
              .in('id', assessment.questoes_ids)

            if (questionsError) throw questionsError
            
            console.log('Questões encontradas:', questionsData?.length)

            // Ordenar questões conforme ordem dos IDs
            const orderedQuestions = assessment.questoes_ids.map((id: string) => 
              questionsData?.find(q => q.id === id)
            ).filter(Boolean)
            
            console.log('Questões ordenadas:', orderedQuestions.length)

            // Combinar questões com blocos de texto
            const textBlocks = assessment.configuracao?.textBlocks || []
            console.log('Blocos de texto:', textBlocks.length)
            
            // Garantir que blocos de texto tenham o formato correto
            const formattedTextBlocks = textBlocks.map((block: any) => ({
              ...block,
              type: 'text' as const
            }))
            
            const allItems: AssessmentItem[] = [...orderedQuestions, ...formattedTextBlocks]
            console.log('Total de items para carregar:', allItems.length)
            
            setSelectedItems(allItems)
          } else {
            // Se não há questões, carregar apenas blocos de texto se existirem
            console.log('Sem questões, verificando blocos de texto...')
            const textBlocks = assessment.configuracao?.textBlocks || []
            if (textBlocks.length > 0) {
              console.log('Carregando apenas blocos de texto:', textBlocks.length)
              const formattedTextBlocks = textBlocks.map((block: any) => ({
                ...block,
                type: 'text' as const
              }))
              setSelectedItems(formattedTextBlocks)
            } else {
              console.log('Nenhum conteúdo para carregar')
              setSelectedItems([])
            }
          }

          // Configurar dados da avaliação
          const config = assessment.configuracao || {}
          setAssessmentConfig({
            titulo: assessment.titulo,
            disciplina: assessment.disciplina || '',
            serie: assessment.serie || '',
            headerConfig: {
              nomeEscola: config.headerConfig?.nomeEscola || 'Nome da Escola',
              nomeProva: assessment.titulo,
              serie: assessment.serie || '',
              data: config.headerConfig?.data || new Date().toLocaleDateString('pt-BR'),
              instrucoes: config.headerConfig?.instrucoes || 'Leia atentamente cada questão antes de responder.'
            },
            pdfOptions: {
              paperSize: config.pdfOptions?.paperSize || 'A4',
              orientation: config.pdfOptions?.orientation || 'portrait',
              fontSize: config.pdfOptions?.fontSize || 'medium',
              fontFamily: config.pdfOptions?.fontFamily || 'times',
              lineSpacing: config.pdfOptions?.lineSpacing || 'normal',
              includeAnswerSheet: config.pdfOptions?.includeAnswerSheet ?? true,
              generateVersions: config.pdfOptions?.generateVersions || 1,
              watermark: config.pdfOptions?.watermark || ''
            },
            showFooter: config.showFooter ?? true
          })

          toast.success('Avaliação carregada!', { id: 'load-assessment' })
        } catch (error: any) {
          console.error('Erro ao carregar avaliação:', error)
          toast.error(`Erro ao carregar avaliação: ${error.message}`, { id: 'load-assessment' })
        }
      }

      loadAssessment()
    } else if (templateId && templates.length > 0) {
      const selectedTemplate = templates.find(t => t.id === templateId)
      if (selectedTemplate) {
        // Apply template with enhanced structure
        try {
          const templateContent = selectedTemplate.content || {}
          const layoutConfig = selectedTemplate.layout_config || {} // Fallback for legacy templates

          // Apply structure settings
          if (templateContent.structure) {
            // Structure settings are applied automatically
          }

          // Apply layout settings
          if (templateContent.layout) {
            setAssessmentConfig(prev => ({
              ...prev,
              pdfOptions: {
                ...prev.pdfOptions,
                paperSize: templateContent.layout.paperSize || 'A4',
                orientation: templateContent.layout.orientation || 'portrait',
                fontSize: templateContent.layout.fontSize || 'medium',
                fontFamily: templateContent.layout.fontFamily || 'times',
                lineSpacing: templateContent.layout.lineSpacing || 'normal',
                includeAnswerSheet: templateContent.structure?.includeAnswerSheet || prev.pdfOptions.includeAnswerSheet,
                generateVersions: 1
              }
            }))
          }

          // Apply customization settings
          if (templateContent.customization?.headerConfig) {
            setAssessmentConfig(prev => ({
              ...prev,
              headerConfig: {
                ...prev.headerConfig,
                ...templateContent.customization.headerConfig
              }
            }))
          }

          // Load template questions if included
          if (templateContent.questions?.includeQuestions && templateContent.questions.questionIds?.length > 0) {
            const loadTemplateQuestions = async () => {
              try {
                const { data: templateQuestions, error } = await supabase
                  .from('questions')
                  .select('*')
                  .in('id', templateContent.questions.questionIds)
                  .eq('status', 'approved')

                if (error) throw error

                if (templateQuestions && templateQuestions.length > 0) {
                  setSelectedItems(templateQuestions)
                  toast.success(`Template "${selectedTemplate.nome}" carregado com ${templateQuestions.length} questões!`)
                } else {
                  toast.success(`Template "${selectedTemplate.nome}" carregado!`)
                }
              } catch (error) {
                console.error('Error loading template questions:', error)
                toast.success(`Template "${selectedTemplate.nome}" carregado! (Algumas questões podem não estar disponíveis)`)
              }
            }

            loadTemplateQuestions()
          } else {
            toast.success(`Template "${selectedTemplate.nome}" carregado!`)
          }

        } catch (error) {
          console.error('Error applying template:', error)
          toast.error('Erro ao carregar template')
        }
      } else {
        toast.error('Template não encontrado')
      }
    }
  }, [location.search, templates])

  // useEffect para adicionar questões selecionadas ao montar o editor
  useEffect(() => {
    if (selectedIds.length > 0 && selectedItems.length === 0 && questions.length > 0) {
      const selectedQuestions = questions.filter(q => selectedIds.includes(q.id))

      if (selectedQuestions.length > 0) {
        const convertedQuestions = selectedQuestions.map(convertQuestionToAssessmentItem)
        setSelectedItems(convertedQuestions)
        clearSelection()
      }
    }
  }, [selectedIds, questions, selectedItems.length, clearSelection])

  useEffect(() => {
    setVisibleCount(10)
  }, [searchTerm, filters, questions])

  const addQuestion = (question: Question) => {
    if (!selectedItems.find(item => 'id' in item && item.id === question.id)) {
      // Usar função de conversão
      const assessmentQuestion = convertQuestionToAssessmentItem(question)

      setSelectedItems([...selectedItems, assessmentQuestion])
      toast.success('Questão adicionada à avaliação')

      // Scroll to the new item
      setTimeout(() => {
        newItemRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
      }, 100)
    } else {
      toast.error('Questão já foi adicionada')
    }
  }

  const removeItem = (index: number) => {
    const newItems = [...selectedItems]
    newItems.splice(index, 1)
    setSelectedItems(newItems)
    toast.success('Item removido da avaliação')
  }

  // Funções para reordenar itens
  const moveItemUp = (index: number) => {
    if (index === 0) return // Não pode mover para cima se já for o primeiro item
    const newItems = [...selectedItems]
    const [movedItem] = newItems.splice(index, 1)
    newItems.splice(index - 1, 0, movedItem)
    setSelectedItems(newItems)
  }

  const moveItemDown = (index: number) => {
    if (index === selectedItems.length - 1) return // Não pode mover para baixo se já for o último item
    const newItems = [...selectedItems]
    const [movedItem] = newItems.splice(index, 1)
    newItems.splice(index + 1, 0, movedItem)
    setSelectedItems(newItems)
  }

  const openNameModal = () => {
    setTempAssessmentTitle(assessmentConfig.titulo)
    setShowNameModal(true)
  }

  const confirmAndSave = async () => {
    setAssessmentConfig(prev => ({ ...prev, titulo: tempAssessmentTitle }))
    setShowNameModal(false)
    await performSaveAssessment()
  }

  // Extraída lógica de salvamento existente
  const performSaveAssessment = async () => {
    if (selectedItems.length === 0) {
      toast.error('Adicione pelo menos um item à avaliação')
      return
    }

    // Check usage limits for free users
    const canCreate = await checkCanPerformAction('create_assessment')
    if (!canCreate) {
      return // Error message already shown by checkCanPerformAction
    }

    try {
      const questionIds = selectedItems
        .filter(item => 'tipo' in item && item.tipo !== undefined)
        .map(item => (item as Question).id)

      const textBlocks = selectedItems
        .filter(item => 'type' in item && item.type === 'text')
        .map(item => item as TextBlock)

      await createAssessment({
        titulo: tempAssessmentTitle,
        disciplina: assessmentConfig.disciplina || 'Geral',
        serie: assessmentConfig.serie || 'Geral',
        questoes_ids: questionIds,
        configuracao: {
          ...assessmentConfig,
          titulo: tempAssessmentTitle,
          textBlocks
        },
        is_public: isAdmin,
        versoes: 1,
        estatisticas: {},
        metadata: {}
      })
      
      // Track usage with new system
      await trackUsage('assessment_created', {
        titulo: tempAssessmentTitle,
        disciplina: assessmentConfig.disciplina,
        serie: assessmentConfig.serie,
        questoes_count: questionIds.length,
        text_blocks_count: textBlocks.length
      })
      
      // Reset form
      setSelectedItems([])
      setAssessmentConfig(prev => ({ ...prev, titulo: 'Nova Avaliação' }))
      
      toast.success('Avaliação salva com sucesso!')
    } catch (error) {
      console.error('Error saving assessment:', error)
      toast.error('Erro ao salvar avaliação')
    }
  }

  const isPaid = isPremium || isEscolar;

  const handleGeneratePDF = async () => {
    if (selectedItems.length === 0) {
      toast.error('Adicione itens à avaliação antes de gerar o PDF')
      return
    }

    // Check usage limits for free users
    const canDownload = await checkCanPerformAction('download_pdf')
    if (!canDownload) {
      return // Error message already shown by checkCanPerformAction
    }

    try {
      toast.loading('Gerando PDF...', { id: 'pdf-generation' })

      // Preparar configuração do cabeçalho com URLs das imagens (mesmo que AssessmentPreview)
      const customization = assessmentConfig.headerConfig.customization;

      const headerConfigWithUrls = {
        ...assessmentConfig.headerConfig,
        customization: customization ? {
          customHeader: customization.customHeader?.enabled && customization.customHeader.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.customHeader.asset.file_path)
          } : customization.customHeader ? {
            enabled: customization.customHeader.enabled || false
          } : undefined,
          schoolLogo: customization.schoolLogo?.enabled && customization.schoolLogo.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.schoolLogo.asset.file_path)
          } : customization.schoolLogo ? {
            enabled: customization.schoolLogo.enabled || false
          } : undefined
        } : undefined
      };

      // Passa todos os selectedItems diretamente para generatePDF
      const blob = await generatePDF(selectedItems, {
        ...assessmentConfig.pdfOptions,
        headerConfig: headerConfigWithUrls,
        showFooter: isPaid ? assessmentConfig.showFooter : true,
        watermark: isPaid ? undefined : WATERMARK_CONFIG.FREE_PLAN_TEXT
      })

      downloadPDF(blob, `${assessmentConfig.titulo}.pdf`)

      // Track PDF download with new system
      await trackUsage('pdf_downloaded', {
        assessment_title: assessmentConfig.titulo,
        questions_count: selectedItems.filter(item => 'tipo' in item && item.tipo !== undefined).length,
        text_blocks_count: selectedItems.length - selectedItems.filter(item => 'tipo' in item && item.tipo !== undefined).length
      })
      
      toast.success('PDF gerado com sucesso!', { id: 'pdf-generation' })
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Erro ao gerar PDF', { id: 'pdf-generation' })
    }
  }

  const handleAddTextBlock = (textBlock: TextBlock) => {
    if (editingTextBlock) {
      const updatedItems = selectedItems.map(item =>
        'id' in item && item.id === editingTextBlock.id ? { ...item, content: textBlock.content, style: textBlock.style, textAlign: textBlock.textAlign } : item
      );
      setSelectedItems(updatedItems as AssessmentItem[]);
      toast.success('Bloco de texto atualizado!');
    } else {
      setSelectedItems([...selectedItems, textBlock])
      toast.success('Bloco de texto adicionado à avaliação')
    }
    setShowTextBlockModal(false)
    setEditingTextBlock(null)
    setTimeout(() => {
      newItemRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }, 100)
  }

  const handleEditTextBlock = (index: number, blockToEdit: TextBlock) => {
    setEditingTextBlock(blockToEdit)
    setShowTextBlockModal(true)
  }

  const handleInsertCustomQuestion = (customQuestion: any) => {
    // Converter questão customizada para o formato Question (types/assessment.ts)
    const questionItem: AssessmentItem = {
      id: customQuestion.id,
      enunciado: customQuestion.enunciado,
      tipo: customQuestion.tipo,
      opcoes: customQuestion.alternativas || undefined,
      resposta_correta: customQuestion.resposta_correta,
      disciplina: customQuestion.disciplina || 'Personalizada',
      serie: customQuestion.serie || 'Geral',
      dificuldade: customQuestion.dificuldade === 'Fácil' ? 'facil' :
                   customQuestion.dificuldade === 'Médio' ? 'medio' : 'dificil',
      tags: customQuestion.tags || [],
      imagem_url: null,
      explicacao: 'Questão criada pelo professor',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      autor_id: user?.id || '',
      status: 'approved' as const,
      // Adicionar metadata para linhas personalizadas em questões dissertativas
      ...(customQuestion.linhas_resposta && {
        metadata: { linhas_resposta: customQuestion.linhas_resposta }
      })
    }

    setSelectedItems([...selectedItems, questionItem])
    toast.success('Questão personalizada adicionada à avaliação')
    setShowCustomQuestionModal(false)

    setTimeout(() => {
      newItemRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }, 100)
  }

  const handleInsertAndSaveCustomQuestion = async (customQuestion: any, questionData: any) => {
    try {
      // Primeiro salvar no banco
      const { data, error } = await supabase
        .from('questions')
        .insert(questionData)
        .select()
        .single()

      if (error) throw error

      // Depois adicionar à avaliação usando os dados salvos
      const questionItem: AssessmentItem = {
        ...data,
        metadata: customQuestion.linhas_resposta ? { linhas_resposta: customQuestion.linhas_resposta } : data.metadata
      }

      setSelectedItems([...selectedItems, questionItem])
      toast.success('Questão salva no banco e adicionada à avaliação')
      setShowCustomQuestionModal(false)

      setTimeout(() => {
        newItemRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' })
      }, 100)
    } catch (error) {
      console.error('Erro ao salvar questão:', error)
      toast.error('Erro ao salvar questão no banco de dados')
    }
  }

  const handleViewQuestionDetail = (question: Question) => {
    setSelectedQuestion(question)
    setShowQuestionDetail(true)
  }



  const handleEditQuestion = (question: Question) => {
    // In a real implementation, this would open an edit modal
    // For now, we'll just show a toast
    toast('Funcionalidade de edição em desenvolvimento')
  }

  const handleSaveAsTemplate = async (templateData: CreateTemplateDTO) => {
    try {
      const { data: user } = await supabase.auth.getUser()

      const insertData = {
        nome: templateData.nome,
        categoria: templateData.categoria,
        descricao: templateData.descricao || null,
        content: templateData.content,
        metadata: {
          ...templateData.metadata,
          createdAt: new Date().toISOString()
        },
        layout_config: templateData.content.layout, // Backward compatibility
        is_premium: templateData.is_premium || false,
        is_system: templateData.is_system || false,
        autor_id: user.user?.id || null,
        tags: templateData.tags || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { error } = await supabase
        .from('templates')
        .insert(insertData)

      if (error) throw error

      toast.success('Template salvo com sucesso!')
      setShowSaveAsTemplateModal(false)
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Erro ao salvar template')
    }
  }

  const getAISuggestions = () => {
    if (!canAccess('ai_generation')) {
      toast.error('Recurso disponível apenas para assinantes Premium')
      return
    }

    // In a real implementation, this would call an AI service
    toast.info('Gerando sugestões de questões...', { duration: 2000 })
    
    setTimeout(() => {
      toast.success('Sugestões geradas! Confira no banco de questões.')
    }, 2000)
  }

  const filteredQuestions = questions.filter(question => 
    !selectedItems.some(item => 'id' in item && item.id === question.id)
  )

  // Count questions (excluding text blocks)
  const questionCount = selectedItems.filter(item => 'tipo' in item).length

  // Check if the currently selected question in the modal is already in the assessment
  const isSelectedQuestionInAssessment = selectedQuestion ? selectedItems.some(item => 'id' in item && item.id === selectedQuestion.id) : false;

  // Mantém lógica original, mas chamaremos performSaveAssessment
  
  const handleSaveClick = () => {
    openNameModal()
  }

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4 lg:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-4 min-w-0">
            <div className="relative flex-1 min-w-0">
              <input
                type="text"
                value={assessmentConfig.titulo}
                onChange={(e) => setAssessmentConfig(prev => ({ ...prev, titulo: e.target.value }))}
                placeholder="Digite o título da avaliação..."
                className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 min-w-0 flex-1 w-full transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 pointer-events-none">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">
              <div className="flex items-center space-x-1">
                <FileText className="w-4 h-4" />
                <span>{questionCount}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>~{questionCount * 2}min</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 lg:space-x-3 overflow-x-auto">
            <button
              onClick={() => setShowQuestionBank(!showQuestionBank)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors whitespace-nowrap ${
                showQuestionBank
                  ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Search className="w-4 h-4" />
              <span className="hidden sm:inline">Questões</span>
            </button>

            <button
              onClick={() => setShowTextBlockModal(true)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors whitespace-nowrap"
            >
              <AlignLeft className="w-4 h-4" />
              <span className="hidden sm:inline">Texto</span>
            </button>

            <button
              onClick={() => setShowCustomQuestionModal(true)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors whitespace-nowrap"
            >
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">Nova Questão</span>
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors whitespace-nowrap"
            >
              <Settings className="w-4 h-4" />
              <span className="hidden sm:inline">Configurar</span>
            </button>

            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors whitespace-nowrap ${
                showPreview
                  ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Eye className="w-4 h-4" />
              <span>Ver</span>
            </button>

            <button
              onClick={() => setShowSaveAsTemplateModal(true)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors whitespace-nowrap"
            >
              <FileText className="w-4 h-4" />
              <span className="hidden sm:inline">Salvar como Modelo</span>
            </button>

            <div className="relative">
              <button
                onClick={handleSaveClick}
                disabled={isCreating || (!isPaidUser && !limitStatus.canCreateAssessment)}
                className={`flex items-center space-x-2 px-3 lg:px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                  !isPaidUser && !limitStatus.canCreateAssessment
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white'
                }`}
                title={!isPaidUser && !limitStatus.canCreateAssessment ? 'Limite de avaliações atingido' : ''}
              >
                <Save className="w-4 h-4" />
                <span className="hidden sm:inline">Salvar</span>
                {!isPaidUser && !limitStatus.canCreateAssessment && (
                  <AlertTriangle className="w-4 h-4 text-yellow-400" />
                )}
              </button>
            </div>

            <div className="relative">
              <button
                onClick={handleGeneratePDF}
                disabled={!isPaidUser && !limitStatus.canDownloadPDF}
                className={`flex items-center space-x-2 px-3 lg:px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                  !isPaidUser && !limitStatus.canDownloadPDF
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
                title={!isPaidUser && !limitStatus.canDownloadPDF ? 'Limite de downloads atingido' : ''}
              >
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline">PDF</span>
                {!isPaidUser && !limitStatus.canDownloadPDF && (
                  <AlertTriangle className="w-4 h-4 text-yellow-400" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Question Bank Sidebar */}
        <AnimatePresence>
          {showQuestionBank && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 400, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col"
              style={{ height: 'calc(100vh - 120px)' }}
            >
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white">Banco de Questões</h3>
                  <button
                    onClick={() => setShowQuestionBank(false)}
                    className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                  >
                    <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  </button>
                </div>
                
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Buscar questões..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      value={filters.disciplina}
                      onChange={(e) => setFilters(prev => ({ ...prev, disciplina: (e.target as HTMLSelectElement).value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                      title="Filtrar por disciplina"
                      aria-label="Filtrar por disciplina"
                    >
                      <option value="">Disciplina</option>
                      {DISCIPLINAS.map((disciplina) => (
                        <option key={disciplina} value={disciplina}>{disciplina}</option>
                      ))}
                    </select>
                    
                    <select
                      value={filters.serie}
                      onChange={(e) => setFilters(prev => ({ ...prev, serie: (e.target as HTMLSelectElement).value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                      title="Filtrar por série"
                      aria-label="Filtrar por série"
                    >
                      <option value="">Série</option>
                      {SERIES.map((serie) => (
                        <option key={serie} value={serie}>{serie}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      value={filters.dificuldade}
                      onChange={(e) => setFilters(prev => ({ ...prev, dificuldade: e.target.value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Dificuldade</option>
                      <option value="Fácil">Fácil</option>
                      <option value="Médio">Médio</option>
                      <option value="Difícil">Difícil</option>
                    </select>
                    
                    <select
                      value={filters.tipo}
                      onChange={(e) => setFilters(prev => ({ ...prev, tipo: e.target.value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Tipo</option>
                      <option value="multipla_escolha">Múltipla Escolha</option>
                      <option value="dissertativa">Dissertativa</option>
                      <option value="verdadeiro_falso">Verdadeiro/Falso</option>
                    </select>
                  </div>
                </div>

                {canAccess('ai_generation') && (
                  <button
                    onClick={getAISuggestions}
                    className="flex items-center justify-center space-x-2 w-full mt-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                  >
                    <Sparkles className="w-4 h-4" />
                    <span>Sugestões de IA</span>
                  </button>
                )}
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="w-6 h-6 border-2 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Carregando...</p>
                  </div>
                ) : filteredQuestions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Nenhuma questão encontrada</p>
                  </div>
                ) : (
                  filteredQuestions.slice(0, visibleCount).map((question) => (
                    <QuestionCard
                      key={question.id}
                      question={question}
                      variant="list"
                      onAddToAssessment={() => addQuestion(question)}
                      onViewDetail={() => handleViewQuestionDetail(question)}
                    />
                  ))
                )}

                {/* Botão Carregar Mais */}
                {filteredQuestions.length > visibleCount && (
                  <button
                    onClick={() => setVisibleCount(prev => prev + 10)}
                    className="w-full mt-4 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600"
                  >
                    Carregar mais
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Editor Area */}
        <div className="flex-1 flex overflow-hidden">
          <div className={`${showPreview ? 'w-1/2' : 'w-full'} flex flex-col`} style={{ height: 'calc(100vh - 120px)' }}>
            {selectedItems.length === 0 ? (
              <div className="h-full overflow-y-auto">
                <div className="min-h-full flex items-center justify-center p-4 lg:p-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white dark:bg-gray-800 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700 p-8 lg:p-12 text-center max-w-md mx-auto"
                  >
                    <div>
                      <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Plus className="w-8 h-8 lg:w-10 lg:h-10 text-gray-400 dark:text-gray-500" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Sua avaliação está vazia
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Adicione questões do banco ou blocos de texto para começar a criar sua avaliação.
                      </p>
                      <div className="flex flex-wrap justify-center gap-3">
                        <button
                          onClick={() => setShowQuestionBank(true)}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          Buscar Questões
                        </button>
                        <button
                          onClick={() => setShowTextBlockModal(true)}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          Adicionar Texto
                        </button>
                        <button
                          onClick={() => setShowCustomQuestionModal(true)}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          Nova Questão
                        </button>
                      </div>
                    </div>
                  </motion.div>

                  {/* Usage Dashboard for Free Users */}
                  {!isPaidUser && (
                    <div className="mt-6">
                      <UsageDashboard variant="compact" />
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col overflow-hidden p-4 lg:p-6">
                <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 flex flex-col h-full">
                  <VirtualizedAssessmentItems
                    selectedItems={selectedItems}
                    moveItemUp={moveItemUp}
                    moveItemDown={moveItemDown}
                    removeItem={removeItem}
                    handleEditTextBlock={handleEditTextBlock}
                    handleViewQuestionDetail={handleViewQuestionDetail}
                  />

                  <div className="p-4 lg:p-6 border-t border-gray-200 dark:border-gray-700 flex flex-wrap gap-3 flex-shrink-0">
                    <button
                      onClick={() => setShowTextBlockModal(true)}
                      className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                    >
                      <AlignLeft className="w-4 h-4" />
                      <span>Adicionar Texto</span>
                    </button>
                    <button
                      onClick={() => setShowCustomQuestionModal(true)}
                      className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Nova Questão</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Preview Panel */}
          <AnimatePresence>
            {showPreview && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: '50%', opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-hidden"
              >
                <AssessmentPreview
                  items={selectedItems}
                  config={{ ...assessmentConfig, showFooter: isPaid ? assessmentConfig.showFooter : true }}
                  onClose={() => setShowPreview(false)}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Settings Modal */}
      <AssessmentSettings
        isOpen={showSettings}
        config={assessmentConfig}
        onConfigChange={setAssessmentConfig}
        onClose={() => setShowSettings(false)}
      />

      {/* Question Detail Modal */}
      <QuestionDetailModal
        isOpen={showQuestionDetail}
        onClose={() => setShowQuestionDetail(false)}
        question={selectedQuestion}
        onAddToAssessment={selectedQuestion ? () => addQuestion(selectedQuestion) : undefined}
        isQuestionInAssessment={isSelectedQuestionInAssessment}
        onRemoveFromAssessment={selectedQuestion ? () => {
          const indexToRemove = selectedItems.findIndex(item => 'id' in item && item.id === selectedQuestion.id);
          if (indexToRemove !== -1) {
            removeItem(indexToRemove);
            setShowQuestionDetail(false);
          }
        } : undefined}
      />

      {/* Text Block Modal */}
      <TextBlockModal
        isOpen={showTextBlockModal}
        onClose={() => setShowTextBlockModal(false)}
        onSave={handleAddTextBlock}
      />

      {/* Custom Question Modal */}
      <CustomQuestionModal
        isOpen={showCustomQuestionModal}
        onClose={() => setShowCustomQuestionModal(false)}
        onInsertOnly={handleInsertCustomQuestion}
        onInsertAndSave={handleInsertAndSaveCustomQuestion}
      />

      {/* Save As Template Modal */}
      <SaveAsTemplateModal
        isOpen={showSaveAsTemplateModal}
        onClose={() => setShowSaveAsTemplateModal(false)}
        onSave={handleSaveAsTemplate}
        config={assessmentConfig}
        selectedItems={selectedItems}
      />

      {showNameModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.9 }} className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Definir Nome da Avaliação</h2>
            <input
              type="text"
              value={tempAssessmentTitle}
              onChange={(e) => setTempAssessmentTitle(e.target.value)}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-md p-2 mb-4 dark:bg-gray-700 dark:text-white"
              placeholder="Ex: Avaliação de Matemática - 2º Bimestre"
            />
            <div className="flex justify-end space-x-3">
              <button onClick={() => setShowNameModal(false)} className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">Cancelar</button>
              <button onClick={confirmAndSave} className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md disabled:opacity-50" disabled={!tempAssessmentTitle.trim() || isCreating}>
                {isCreating ? 'Salvando...' : 'Salvar'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default AssessmentEditor