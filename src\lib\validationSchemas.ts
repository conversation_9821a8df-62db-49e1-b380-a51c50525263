import { z } from 'zod'

// Schemas básicos reutilizáveis
export const emailSchema = z.string()
  .email('Por favor, insira um email válido')
  .min(1, 'Email é obrigatório')

export const passwordSchema = z.string()
  .min(6, 'A senha deve ter pelo menos 6 caracteres')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'A senha deve conter pelo menos uma letra minúscula, uma maiúscula e um número')

export const nameSchema = z.string()
  .min(2, 'Nome deve ter pelo menos 2 caracteres')
  .max(100, 'Nome deve ter no máximo 100 caracteres')
  .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços')

export const schoolNameSchema = z.string()
  .min(2, 'Nome da escola deve ter pelo menos 2 caracteres')
  .max(200, 'Nome da escola deve ter no máximo 200 caracteres')

// Schemas de autenticação
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Senha é obrigatória')
})

export const registerSchema = z.object({
  nome: nameSchema,
  email: emailSchema,
  escola: schoolNameSchema.optional(),
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"]
})

export const forgotPasswordSchema = z.object({
  email: emailSchema
})

export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"]
})

// Schema de questões
export const questionSchema = z.object({
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  topico: z.string().min(1, 'Tópico é obrigatório'),
  subtopico: z.string().optional(),
  dificuldade: z.enum(['Fácil', 'Médio', 'Difícil'], {
    errorMap: () => ({ message: 'Selecione uma dificuldade válida' })
  }),
  tipo: z.enum(['multipla_escolha', 'dissertativa', 'verdadeiro_falso'], {
    errorMap: () => ({ message: 'Selecione um tipo de questão válido' })
  }),
  competencia_bncc: z.string().optional(),
  enunciado: z.string()
    .min(10, 'O enunciado deve ter pelo menos 10 caracteres')
    .max(2000, 'O enunciado deve ter no máximo 2000 caracteres'),
  alternativas: z.array(z.string()).optional(),
  resposta_correta: z.string().min(1, 'Resposta correta é obrigatória'),
  explicacao: z.string()
    .min(10, 'A explicação deve ter pelo menos 10 caracteres')
    .max(1000, 'A explicação deve ter no máximo 1000 caracteres'),
  tags: z.array(z.string()).default([]),
  visibility: z.enum(['private', 'public', 'school']).default('private'),
  is_public: z.boolean().default(true),
  is_verified: z.boolean().default(false)
}).refine((data) => {
  // Validação condicional para questões de múltipla escolha
  if (data.tipo === 'multipla_escolha') {
    return data.alternativas && data.alternativas.length >= 2 && 
           data.alternativas.every(alt => alt.trim().length > 0)
  }
  return true
}, {
  message: 'Questões de múltipla escolha devem ter pelo menos 2 alternativas preenchidas',
  path: ['alternativas']
})

// Schema de avaliações
export const assessmentSchema = z.object({
  titulo: z.string()
    .min(3, 'Título deve ter pelo menos 3 caracteres')
    .max(200, 'Título deve ter no máximo 200 caracteres'),
  disciplina: z.string().min(1, 'Disciplina é obrigatória'),
  serie: z.string().min(1, 'Série é obrigatória'),
  descricao: z.string()
    .max(500, 'Descrição deve ter no máximo 500 caracteres')
    .optional(),
  is_public: z.boolean().default(false),
  questoes: z.array(z.string()).min(1, 'Selecione pelo menos uma questão')
})

// Schema de templates
export const templateSchema = z.object({
  nome: z.string()
    .min(3, 'Nome deve ter pelo menos 3 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres'),
  descricao: z.string()
    .max(500, 'Descrição deve ter no máximo 500 caracteres')
    .optional(),
  categoria: z.string().min(1, 'Categoria é obrigatória'),
  tags: z.array(z.string()).default([]),
  is_public: z.boolean().default(false),
  is_premium: z.boolean().default(false),
  is_system: z.boolean().default(false),
  preview_image: z.string().optional(),
  configuracao: z.object({
    titulo: z.string().optional(),
    subtitulo: z.string().optional(),
    instrucoes: z.string().optional(),
    showHeader: z.boolean().default(true),
    showFooter: z.boolean().default(true),
    fontFamily: z.enum(['Helvetica', 'Times', 'Arial', 'Verdana', 'Georgia', 'Courier']).default('Helvetica'),
    fontSize: z.enum(['small', 'medium', 'large']).default('medium')
  }).optional()
})

// Schema de perfil de usuário
export const profileSchema = z.object({
  nome: nameSchema,
  email: emailSchema,
  escola: schoolNameSchema,
  disciplinas: z.array(z.string())
    .min(1, 'Selecione pelo menos uma disciplina')
    .max(10, 'Selecione no máximo 10 disciplinas'),
  series: z.array(z.string())
    .min(1, 'Selecione pelo menos uma série')
    .max(15, 'Selecione no máximo 15 séries')
})

// Schema de configurações de segurança
export const securitySchema = z.object({
  currentPassword: z.string().min(1, 'Senha atual é obrigatória'),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"]
})

// Schema de notificações
export const notificationSchema = z.object({
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(false),
  newQuestionNotifications: z.boolean().default(true),
  systemUpdatesNotifications: z.boolean().default(true),
  weeklyReportNotifications: z.boolean().default(false),
  marketingNotifications: z.boolean().default(false)
})

// Schema de usuário (admin)
export const userSchema = z.object({
  nome: nameSchema,
  email: emailSchema,
  escola: schoolNameSchema.optional(),
  disciplinas: z.array(z.string()).default([]),
  series: z.array(z.string()).default([]),
  plano: z.enum(['gratuito', 'premium', 'escolar']).default('gratuito'),
  is_admin: z.boolean().default(false),
  is_active: z.boolean().default(true)
})

// Schema de feedback
export const feedbackSchema = z.object({
  rating: z.number().min(1, 'Avaliação é obrigatória').max(5, 'Avaliação deve ser entre 1 e 5'),
  comment: z.string()
    .min(10, 'Comentário deve ter pelo menos 10 caracteres')
    .max(1000, 'Comentário deve ter no máximo 1000 caracteres'),
  category: z.enum(['bug', 'feature', 'improvement', 'other']).default('other'),
  question_id: z.string().optional()
})

// Schema de configurações do sistema (admin)
export const systemSettingsSchema = z.object({
  siteName: z.string().min(1, 'Nome do site é obrigatório'),
  siteDescription: z.string().max(500, 'Descrição deve ter no máximo 500 caracteres'),
  maintenanceMode: z.boolean().default(false),
  registrationEnabled: z.boolean().default(true),
  maxQuestionsPerUser: z.number().min(1).max(10000).default(1000),
  maxAssessmentsPerUser: z.number().min(1).max(1000).default(100),
  defaultUserPlan: z.enum(['gratuito', 'premium', 'escolar']).default('gratuito')
})

// Tipos TypeScript derivados dos schemas
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type QuestionFormData = z.infer<typeof questionSchema>
export type AssessmentFormData = z.infer<typeof assessmentSchema>
export type TemplateFormData = z.infer<typeof templateSchema>
export type ProfileFormData = z.infer<typeof profileSchema>
export type SecurityFormData = z.infer<typeof securitySchema>
export type NotificationFormData = z.infer<typeof notificationSchema>
export type UserFormData = z.infer<typeof userSchema>
export type FeedbackFormData = z.infer<typeof feedbackSchema>
export type SystemSettingsFormData = z.infer<typeof systemSettingsSchema>
