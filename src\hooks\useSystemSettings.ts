import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { z } from 'zod'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useAuditLog } from './useAuditLog'
import toast from 'react-hot-toast'

// 🔧 ARCHITECTURE: Types for the hook (imported from SystemSettings)
type SettingKey = 
  | 'platform_name'
  | 'maintenance_mode'
  | 'enable_ai_generation'
  | 'enable_collaboration'
  | 'max_questions_per_assessment'
  | 'default_question_limit_free'

type SettingValue<T extends SettingKey> = 
  T extends 'platform_name' ? string :
  T extends 'maintenance_mode' | 'enable_ai_generation' | 'enable_collaboration' ? boolean :
  T extends 'max_questions_per_assessment' | 'default_question_limit_free' ? number :
  never

type SettingCategory = 'Geral' | 'Limites' | 'Recursos' | 'Outros'

interface SystemSetting<T extends SettingKey = SettingKey> {
  id: string
  key: T
  value: SettingValue<T>
  description: string | null
  updated_by: string | null
  created_at: string
  updated_at: string
}

interface SettingConfig<T extends SettingKey> {
  key: T
  category: SettingCategory
  inputType: 'text' | 'number' | 'boolean'
  validation: z.ZodSchema<SettingValue<T>>
  defaultValue: SettingValue<T>
  description: string
  helpText?: string
}

interface ValidationResult<T = any> {
  isValid: boolean
  sanitizedValue: T
  error?: string
}

interface SettingChangeLog<T extends SettingKey = SettingKey> {
  key: T
  description: string | null
  oldValue: SettingValue<T>
  newValue: SettingValue<T>
}

// 🔧 ARCHITECTURE: Centralized configuration (duplicated for now, will be shared later)
const SETTING_CONFIGS: Record<SettingKey, SettingConfig<any>> = {
  platform_name: {
    key: 'platform_name',
    category: 'Geral',
    inputType: 'text',
    validation: z.string()
      .min(1, 'Nome da plataforma não pode ser vazio')
      .max(100, 'Nome da plataforma deve ter no máximo 100 caracteres')
      .regex(/^[a-zA-ZÀ-ÿ0-9\s\-_\.]+$/, 'Nome da plataforma contém caracteres inválidos')
      .transform(val => val.trim())
      .refine(val => !val.includes('  '), 'Nome não pode conter espaços duplos')
      .refine(val => !/^\s|\s$/.test(val), 'Nome não pode começar ou terminar com espaços')
      .refine(val => val.length >= 2, 'Nome deve ter pelo menos 2 caracteres'),
    defaultValue: 'EduAssess',
    description: 'Nome da plataforma',
    helpText: 'Nome exibido em toda a aplicação'
  },
  maintenance_mode: {
    key: 'maintenance_mode',
    category: 'Geral',
    inputType: 'boolean',
    validation: z.boolean()
      .refine(val => typeof val === 'boolean', 'Valor deve ser verdadeiro ou falso'),
    defaultValue: false,
    description: 'Modo de manutenção ativo',
    helpText: 'Quando ativo, apenas administradores podem acessar o sistema'
  },
  enable_ai_generation: {
    key: 'enable_ai_generation',
    category: 'Recursos',
    inputType: 'boolean',
    validation: z.boolean(),
    defaultValue: true,
    description: 'Habilitar geração de questões por IA',
    helpText: 'Permite que usuários gerem questões usando inteligência artificial'
  },
  enable_collaboration: {
    key: 'enable_collaboration',
    category: 'Recursos',
    inputType: 'boolean',
    validation: z.boolean(),
    defaultValue: true,
    description: 'Habilitar recursos de colaboração',
    helpText: 'Permite compartilhamento e colaboração entre usuários'
  },
  max_questions_per_assessment: {
    key: 'max_questions_per_assessment',
    category: 'Limites',
    inputType: 'number',
    validation: z.number()
      .int('Deve ser um número inteiro')
      .min(1, 'Deve ser maior que 0')
      .max(1000, 'Deve ser menor ou igual a 1000')
      .refine(val => val % 1 === 0, 'Deve ser um número inteiro')
      .refine(val => val > 0, 'Deve ser um número positivo')
      .refine(val => !isNaN(val), 'Deve ser um número válido'),
    defaultValue: 50,
    description: 'Máximo de questões por avaliação',
    helpText: 'Limite máximo de questões que podem ser incluídas em uma avaliação'
  },
  default_question_limit_free: {
    key: 'default_question_limit_free',
    category: 'Limites',
    inputType: 'number',
    validation: z.number()
      .int('Deve ser um número inteiro')
      .min(1, 'Deve ser maior que 0')
      .max(500, 'Deve ser menor ou igual a 500')
      .refine(val => val % 1 === 0, 'Deve ser um número inteiro')
      .refine(val => val > 0, 'Deve ser um número positivo')
      .refine(val => !isNaN(val), 'Deve ser um número válido')
      .refine(val => val <= 500, 'Limite muito alto para usuários gratuitos'),
    defaultValue: 50,
    description: 'Limite de questões para usuários gratuitos',
    helpText: 'Número máximo de questões que usuários gratuitos podem criar por mês'
  }
} as const

// 🔒 SECURITY: Additional validation schemas
const SECURITY_SCHEMAS = {
  // Schema para sanitização de strings
  sanitizeString: z.string()
    .transform(val => val.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ''))
    .transform(val => val.replace(/javascript:/gi, ''))
    .transform(val => val.replace(/on\w+\s*=/gi, ''))
    .transform(val => val.trim()),

  // Schema para validação de números seguros
  safeNumber: z.number()
    .refine(val => !isNaN(val) && isFinite(val), 'Número inválido')
    .refine(val => val >= Number.MIN_SAFE_INTEGER && val <= Number.MAX_SAFE_INTEGER, 'Número fora do intervalo seguro'),

  // Schema para validação de booleanos
  safeBoolean: z.boolean()
    .or(z.string().transform(val => val.toLowerCase() === 'true'))
    .or(z.number().transform(val => val === 1))
}

// 🔒 SECURITY: Error messages
const ERROR_MESSAGES = {
  FETCH_SETTINGS: 'Erro ao carregar configurações do sistema.',
  SAVE_SETTING: 'Erro ao salvar a configuração. Tente novamente.',
  SAVE_SETTINGS: 'Erro ao salvar algumas configurações. Tente novamente.',
  ADMIN_VERIFICATION: 'Erro ao verificar permissões de administrador',
  UNAUTHORIZED_ACCESS: 'Você precisa ter permissões de administrador para acessar as configurações do sistema',
  VALIDATION_FAILED: 'Dados inválidos fornecidos'
} as const

// 🔒 SECURITY: Safe error logging
const logSecureError = (context: string, errorCode?: string) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(`useSystemSettings: ${context}${errorCode ? ` [${errorCode}]` : ''}`)
  } else {
    console.error(`useSystemSettings: Operation failed [${context}]`)
  }
}

// 🔒 SECURITY: Sanitization function
const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .slice(0, 500)
}

// 🔧 ARCHITECTURE: Type-safe validation
const validateSettingValue = <T extends SettingKey>(
  key: T, 
  value: any
): ValidationResult<SettingValue<T>> => {
  try {
    const config = SETTING_CONFIGS[key]
    if (!config) {
      return { isValid: false, sanitizedValue: value, error: 'Configuração não encontrada' }
    }

    let processedValue = value

    if (typeof value === 'string' && config.inputType === 'text' && key !== 'platform_name') {
      processedValue = sanitizeInput(value)
    }

    if (config.inputType === 'number' && typeof value === 'string') {
      const numValue = parseInt(value, 10)
      if (isNaN(numValue)) {
        return { isValid: false, sanitizedValue: value, error: 'Deve ser um número válido' }
      }
      processedValue = numValue
    }

    const result = config.validation.parse(processedValue)
    return { isValid: true, sanitizedValue: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return { isValid: false, sanitizedValue: value, error: firstError.message }
    }
    return { isValid: false, sanitizedValue: value, error: 'Valor inválido' }
  }
}

// 🔧 ARCHITECTURE: Fetch function
const fetchSystemSettings = async (): Promise<SystemSetting[]> => {
  const { data, error } = await supabase
    .from('system_settings')
    .select('*')
    .order('key')

  if (error) {
    logSecureError('fetch_settings', error.code)
    throw new Error(ERROR_MESSAGES.FETCH_SETTINGS)
  }
  return data || []
}

// 🔧 ARCHITECTURE: Main hook
export const useSystemSettings = () => {
  const { user, isAdmin } = useAuth()
  const { logAction, logAdminAccess } = useAuditLog()
  const queryClient = useQueryClient()

  // State management
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [lastSaveAttempt, setLastSaveAttempt] = useState<number>(0)
  const [saveAttempts, setSaveAttempts] = useState<number>(0)

  // 🔍 SEARCH: Search and filter state
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [categoryFilters, setCategoryFilters] = useState<string[]>([])
  const [showModifiedOnly, setShowModifiedOnly] = useState<boolean>(false)

  // ⚡ PERFORMANCE: Refs for debouncing
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const pendingChangesRef = useRef<Record<string, any>>({})

  // 🔧 ARCHITECTURE: React Query for data fetching
  const {
    data: settings = [],
    isLoading,
    error,
    refetch
  } = useQuery<SystemSetting[], Error>({
    queryKey: ['systemSettings'],
    queryFn: fetchSystemSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
    enabled: isAdmin // Only fetch if user is admin
  })

  // 🔧 ARCHITECTURE: Mutation for updating settings
  const updateSettingMutation = useMutation<SystemSetting, Error, { key: string; value: any }>({
    mutationFn: async ({ key, value }) => {
      const { data, error } = await supabase
        .from('system_settings')
        .update({
          value,
          updated_by: user?.id || null
        })
        .eq('key', key)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onError: () => {
      logSecureError('setting_update_failed')
      toast.error(ERROR_MESSAGES.SAVE_SETTING)
    }
  })

  // 🔧 ARCHITECTURE: Update form data when settings change
  useEffect(() => {
    if (settings && settings.length > 0) {
      const newFormData: Record<string, any> = {}
      settings.forEach(setting => {
        newFormData[setting.key] = setting.value
      })
      if (JSON.stringify(newFormData) !== JSON.stringify(formData)) {
        setFormData(newFormData)
      }
    }
  }, [settings, formData])

  // 🔒 SECURITY: Handle errors securely
  useEffect(() => {
    if (error) {
      logSecureError('settings_fetch_failed')
      toast.error(ERROR_MESSAGES.FETCH_SETTINGS)
    }
  }, [error])

  // 🎨 UX: Track modified fields for visual feedback (moved before filteredSettings)
  const modifiedFields = useMemo(() => {
    const modified = new Set<string>()
    settings.forEach(setting => {
      const currentValue = formData[setting.key]
      const originalValue = setting.value

      // Compare values considering type conversions
      let isModified = false
      if (typeof currentValue === 'string' && typeof originalValue === 'number') {
        isModified = parseInt(currentValue, 10) !== originalValue
      } else if (typeof currentValue === 'boolean' && typeof originalValue === 'string') {
        isModified = currentValue !== (originalValue === 'true')
      } else {
        isModified = currentValue !== originalValue
      }

      if (isModified) {
        modified.add(setting.key)
      }
    })
    return modified
  }, [settings, formData])

  // 🔍 SEARCH: Filter settings based on search and filters (now modifiedFields is available)
  const filteredSettings = useMemo(() => {
    let filtered = settings

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(setting => {
        const config = SETTING_CONFIGS[setting.key as SettingKey]
        const description = setting.description || config?.description || ''
        const key = setting.key

        return (
          description.toLowerCase().includes(searchLower) ||
          key.toLowerCase().includes(searchLower) ||
          key.replace(/_/g, ' ').toLowerCase().includes(searchLower)
        )
      })
    }

    // Apply category filters
    if (categoryFilters.length > 0) {
      filtered = filtered.filter(setting => {
        const config = SETTING_CONFIGS[setting.key as SettingKey]
        const category = config?.category || 'Outros'
        return categoryFilters.includes(category)
      })
    }

    // Apply modified filter
    if (showModifiedOnly) {
      filtered = filtered.filter(setting => modifiedFields.has(setting.key))
    }

    return filtered
  }, [settings, searchTerm, categoryFilters, showModifiedOnly, modifiedFields])

  // 🔧 ARCHITECTURE: Memoized grouped settings (now using filtered settings)
  const groupedSettings = useMemo(() => {
    return filteredSettings.reduce((acc, setting) => {
      const config = SETTING_CONFIGS[setting.key as SettingKey]
      const category = config?.category || 'Outros'
      if (!acc[category]) acc[category] = []
      acc[category].push(setting)
      return acc
    }, {} as Record<string, SystemSetting[]>)
  }, [filteredSettings])

  // 🎨 UX: Check if there are any unsaved changes
  const hasUnsavedChanges = modifiedFields.size > 0

  // ⚡ PERFORMANCE: Debounced input change handler with sanitization
  const handleInputChange = useCallback((key: string, value: any) => {
    let sanitizedValue = value

    // Basic sanitization for real-time input
    if (typeof value === 'string' && key !== 'platform_name') {
      sanitizedValue = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    }

    // Immediate update for UI responsiveness
    setFormData(prev => ({ ...prev, [key]: sanitizedValue }))

    // ⚡ PERFORMANCE: Debounce validation for performance
    pendingChangesRef.current[key] = sanitizedValue

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      // Validate pending changes after debounce period
      const validation = validateSettingValue(key as SettingKey, sanitizedValue)
      if (!validation.isValid && process.env.NODE_ENV === 'development') {
        console.warn(`Validation warning for ${key}:`, validation.error)
      }

      // Clear pending changes
      delete pendingChangesRef.current[key]
    }, 300) // 300ms debounce
  }, [])

  // 🔧 ARCHITECTURE: Save handler with rate limiting and validation
  const handleSave = useCallback(async () => {
    // Rate limiting
    const now = Date.now()
    const timeSinceLastSave = now - lastSaveAttempt
    const RATE_LIMIT_MS = 2000
    const MAX_ATTEMPTS_PER_MINUTE = 10

    if (timeSinceLastSave < RATE_LIMIT_MS) {
      toast.error('Aguarde um momento antes de salvar novamente.')
      return
    }

    if (saveAttempts >= MAX_ATTEMPTS_PER_MINUTE) {
      toast.error('Muitas tentativas de salvamento. Aguarde um minuto.')
      logSecureError('rate_limit_exceeded')
      return
    }

    setLastSaveAttempt(now)
    setSaveAttempts(prev => prev + 1)
    setTimeout(() => setSaveAttempts(0), 60000)

    const promises: Promise<any>[] = []
    const originalSettingsMap = new Map(settings.map(s => [s.key, s.value]))
    let hasValidationError = false

    // Validate all settings
    for (const setting of settings) {
      const key = setting.key as SettingKey
      const rawValue = formData[key]

      const validation = validateSettingValue(key, rawValue)

      if (!validation.isValid) {
        const settingName = setting.description || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        toast.error(`${settingName}: ${validation.error}`)
        hasValidationError = true
        continue
      }

      const valueToSave = validation.sanitizedValue

      if (originalSettingsMap.get(key) !== valueToSave) {
        promises.push(updateSettingMutation.mutateAsync({ key, value: valueToSave }))
      }
    }

    if (hasValidationError) {
      return
    }

    if (promises.length === 0) {
      toast.success('Nenhuma alteração a ser salva.')
      return
    }

    try {
      await Promise.all(promises)

      // Audit logging
      const changedSettings = settings.filter(setting => {
        const validation = validateSettingValue(setting.key as SettingKey, formData[setting.key])
        return validation.isValid && originalSettingsMap.get(setting.key) !== validation.sanitizedValue
      })

      if (changedSettings.length > 0) {
        await logAction(
          'UPDATE_SYSTEM_SETTINGS',
          'system_settings',
          {
            changed_settings: changedSettings.map(setting => ({
              key: setting.key,
              description: setting.description,
              old_value: originalSettingsMap.get(setting.key),
              new_value: validateSettingValue(setting.key as SettingKey, formData[setting.key]).sanitizedValue
            })),
            total_changes: changedSettings.length,
            timestamp: new Date().toISOString()
          }
        )
      }

      toast.success('Configurações salvas com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['systemSettings'] })
    } catch (error) {
      logSecureError('settings_save_failed')

      await logAction(
        'FAILED_SYSTEM_SETTINGS_UPDATE',
        'system_settings',
        {
          attempted_changes: settings.length,
          error_type: 'save_failed',
          timestamp: new Date().toISOString()
        }
      )

      toast.error(ERROR_MESSAGES.SAVE_SETTINGS)
    }
  }, [settings, formData, lastSaveAttempt, saveAttempts, updateSettingMutation, logAction, queryClient])

  // 🔧 ARCHITECTURE: Get setting configuration
  const getSettingConfig = useCallback(<T extends SettingKey>(key: T): SettingConfig<T> => {
    return SETTING_CONFIGS[key] as SettingConfig<T>
  }, [])

  // 🔧 ARCHITECTURE: Validate individual setting
  const validateSetting = useCallback(<T extends SettingKey>(key: T, value: any): ValidationResult<SettingValue<T>> => {
    return validateSettingValue(key, value)
  }, [])

  // ⚡ PERFORMANCE: Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  return {
    // Data
    settings,
    filteredSettings,
    groupedSettings,
    formData,

    // Loading states
    isLoading,
    isSaving: updateSettingMutation.isPending,

    // Error state
    error,

    // 🎨 UX: Visual feedback states
    modifiedFields,
    hasUnsavedChanges,

    // 🔍 SEARCH: Search and filter states
    searchTerm,
    categoryFilters,
    showModifiedOnly,

    // Actions
    handleInputChange,
    handleSave,
    refetch,

    // 🔍 SEARCH: Search and filter actions
    setSearchTerm,
    setCategoryFilters,
    setShowModifiedOnly,

    // Utilities
    getSettingConfig,
    validateSetting,

    // Constants
    SETTING_CONFIGS,
    ERROR_MESSAGES
  }
}
