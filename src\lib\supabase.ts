import { createClient } from '@supabase/supabase-js'
import { Database } from '../types/database'

// Função para obter variáveis de ambiente com fallback
const getEnvVar = (key: string, fallback?: string): string => {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[key] || fallback || ''
  }
  // Fallback para casos onde import.meta.env não está disponível
  return fallback || ''
}

const supabaseUrl = getEnvVar('VITE_SUPABASE_URL', 'https://wihmaklmjrylsqurtgwz.supabase.co')
const supabaseAnonKey = getEnvVar('VITE_SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndpaG1ha2xtanJ5bHNxdXJ0Z3d6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NjkwMTAsImV4cCI6MjA2NTQ0NTAxMH0.ROk4K5kbh0A7R8h5tXiQ63ZeNs4kWOyDVhMhF4qWazc')

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables:', { supabaseUrl: !!supabaseUrl, supabaseAnonKey: !!supabaseAnonKey })
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    // Adicionar headers para evitar problemas de CORS
    headers: {
      'X-Client-Info': 'eduassess-platform'
    }
  },
  db: {
    // Desabilitar cache para evitar problemas com RLS
    schema: 'public'
  }
})

// Adicionar função RPC para buscar perfil sem problemas de RLS
export const getProfileById = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()
    
    if (error) throw error
    return data
  } catch (error) {
    console.error('Error in getProfileById:', error)
    return null
  }
}