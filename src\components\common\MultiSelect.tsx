import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, X, Check, Search } from 'lucide-react'
import { FieldError } from 'react-hook-form'

interface Option {
  value: string
  label: string
  disabled?: boolean
}

interface MultiSelectProps {
  label: string
  options: Option[]
  value: string[]
  onChange: (value: string[]) => void
  error?: FieldError
  placeholder?: string
  searchable?: boolean
  maxSelections?: number
  required?: boolean
  helperText?: string
  disabled?: boolean
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  label,
  options,
  value,
  onChange,
  error,
  placeholder = 'Selecione...',
  searchable = false,
  maxSelections,
  required = false,
  helperText,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  const hasError = !!error
  const isMaxReached = maxSelections ? value.length >= maxSelections : false

  // Filtrar opções baseado na busca
  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Focar no input de busca quando abrir
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen, searchable])

  const handleToggleOption = (optionValue: string) => {
    if (disabled) return

    const isSelected = value.includes(optionValue)
    
    if (isSelected) {
      onChange(value.filter(v => v !== optionValue))
    } else if (!isMaxReached) {
      onChange([...value, optionValue])
    }
  }

  const handleRemoveOption = (optionValue: string) => {
    if (disabled) return
    onChange(value.filter(v => v !== optionValue))
  }

  const getSelectedLabels = () => {
    return value.map(v => options.find(opt => opt.value === v)?.label || v)
  }

  const selectedLabels = getSelectedLabels()

  return (
    <div className="space-y-2" ref={dropdownRef}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        {maxSelections && (
          <span className="text-xs text-gray-500 ml-2">
            ({value.length}/{maxSelections})
          </span>
        )}
      </label>

      {/* Selected items display */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedLabels.map((label, index) => (
            <motion.span
              key={value[index]}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {label}
              {!disabled && (
                <button
                  type="button"
                  onClick={() => handleRemoveOption(value[index])}
                  className="ml-1 hover:text-blue-600 dark:hover:text-blue-300"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </motion.span>
          ))}
        </div>
      )}

      {/* Dropdown trigger */}
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={`
            w-full px-4 py-3 text-left border rounded-lg transition-all duration-200
            ${hasError 
              ? 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20' 
              : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
            }
            ${disabled 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:border-gray-400 dark:hover:border-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'
            }
            text-gray-900 dark:text-white focus:outline-none
          `}
        >
          <div className="flex items-center justify-between">
            <span className={value.length === 0 ? 'text-gray-500 dark:text-gray-400' : ''}>
              {value.length === 0 
                ? placeholder 
                : `${value.length} selecionado${value.length > 1 ? 's' : ''}`
              }
            </span>
            <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} />
          </div>
        </button>

        {/* Dropdown menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-hidden"
            >
              {/* Search input */}
              {searchable && (
                <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Buscar..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}

              {/* Options list */}
              <div className="max-h-48 overflow-y-auto">
                {filteredOptions.length === 0 ? (
                  <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                    {searchTerm ? 'Nenhuma opção encontrada' : 'Nenhuma opção disponível'}
                  </div>
                ) : (
                  filteredOptions.map((option) => {
                    const isSelected = value.includes(option.value)
                    const isDisabled = option.disabled || (isMaxReached && !isSelected)

                    return (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => handleToggleOption(option.value)}
                        disabled={isDisabled}
                        className={`
                          w-full px-4 py-3 text-left text-sm transition-colors duration-150
                          ${isSelected 
                            ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                            : 'text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                          }
                          ${isDisabled 
                            ? 'opacity-50 cursor-not-allowed' 
                            : 'cursor-pointer'
                          }
                          flex items-center justify-between
                        `}
                      >
                        <span>{option.label}</span>
                        {isSelected && <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />}
                      </button>
                    )
                  })
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Error message or helper text */}
      <AnimatePresence mode="wait">
        {(error || helperText) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error ? (
              <p className="text-sm text-red-600 dark:text-red-400">
                {error.message}
              </p>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {helperText}
              </p>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default MultiSelect
