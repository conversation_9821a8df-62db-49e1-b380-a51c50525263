import React from 'react'
import { useAuth } from '../../../contexts/AuthContext'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
}

interface AdvancedFiltersProps {
  filters: QuestionFilters
  onFilterChange: (key: keyof QuestionFilters, value: string | string[] | boolean) => void
  isAdmin: boolean
  isSchoolAdmin: boolean
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFilterChange,
  isAdmin,
  isSchoolAdmin
}) => {
  const { user, profile } = useAuth()

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pt-3 border-t border-gray-100 dark:border-gray-700">
      {/* Visibilidade */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Visibilidade
        </label>
        <select
          value={filters.visibility_filter || 'all'}
          onChange={(e) => onFilterChange('visibility_filter', (e.target as HTMLSelectElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por visibilidade"
          aria-label="Filtrar por visibilidade"
        >
          <option value="all">Todas</option>
          <option value="private">Minhas</option>
          <option value="public">Públicas</option>
          {profile?.escola && (
            <option value="school">Da Escola</option>
          )}
          {user && (
            <option value="my_pending_or_rejected">Pendentes/Rejeitadas</option>
          )}
        </select>
      </div>

      {/* Status (Admin) */}
      {isAdmin && (
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status (Admin)
          </label>
          <select
            value={filters.status || 'all'}
            onChange={(e) => onFilterChange('status', (e.target as HTMLSelectElement).value)}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            title="Filtrar por status"
            aria-label="Filtrar por status"
          >
            <option value="all">Todos</option>
            <option value="pending">Pendente</option>
            <option value="approved">Aprovada</option>
            <option value="rejected">Rejeitada</option>
          </select>
        </div>
      )}

      {/* Competência BNCC */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Competência BNCC
        </label>
        <input
          type="text"
          value={filters.competencia_bncc || ''}
          onChange={(e) => onFilterChange('competencia_bncc', (e.target as HTMLInputElement).value)}
          placeholder="Ex: EF06MA07"
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por competência BNCC"
          aria-label="Filtrar por competência BNCC"
        />
      </div>

      {/* Tags */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Tags
        </label>
        <input
          type="text"
          value={filters.tags?.join(', ') || ''}
          onChange={(e) => onFilterChange('tags', (e.target as HTMLInputElement).value.split(',').map(tag => tag.trim()).filter(tag => tag !== ''))}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          placeholder="Ex: matemática, álgebra"
          title="Filtrar por tags"
          aria-label="Filtrar por tags"
        />
      </div>

      {/* Criado Após */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Criado Após
        </label>
        <input
          type="date"
          value={filters.created_after || ''}
          onChange={(e) => onFilterChange('created_after', (e.target as HTMLInputElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por data inicial"
          aria-label="Filtrar por data inicial"
        />
      </div>

      {/* Criado Antes */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Criado Antes
        </label>
        <input
          type="date"
          value={filters.created_before || ''}
          onChange={(e) => onFilterChange('created_before', (e.target as HTMLInputElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por data final"
          aria-label="Filtrar por data final"
        />
      </div>

      {/* Autor ID (Admin) */}
      {isAdmin && (
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            ID do Autor
          </label>
          <input
            type="text"
            value={filters.autor_id || ''}
            onChange={(e) => onFilterChange('autor_id', (e.target as HTMLInputElement).value)}
            className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
            placeholder="ID do usuário"
            title="Filtrar por autor"
            aria-label="Filtrar por autor"
          />
        </div>
      )}

      {/* Verificação */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Verificação
        </label>
        <select
          value={filters.is_verified === true ? 'verified' : filters.is_verified === false ? 'not_verified' : 'all'}
          onChange={(e) => {
            const value = e.target.value
            if (value === 'verified') onFilterChange('is_verified', true)
            else if (value === 'not_verified') onFilterChange('is_verified', false)
            else onFilterChange('is_verified', undefined)
          }}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por verificação"
          aria-label="Filtrar por verificação"
        >
          <option value="all">Todas</option>
          <option value="verified">Verificadas</option>
          <option value="not_verified">Não Verificadas</option>
        </select>
      </div>
    </div>
  )
}

export default AdvancedFilters
