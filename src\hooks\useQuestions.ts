import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { Database } from '../types/database'
import toast from 'react-hot-toast'

type Question = Database['public']['Tables']['questions']['Row']
type QuestionInsert = Database['public']['Tables']['questions']['Insert']
type QuestionUpdate = Database['public']['Tables']['questions']['Update']

interface QuestionFilters {
  disciplina?: string
  serie?: string
  topico?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  is_public?: boolean
  is_verified?: boolean
  autor_id?: string
  created_after?: string
  created_before?: string
  limit?: number
  page?: number
  source?: 'platform' | 'teacher' | 'all'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
  is_shared_with_school?: boolean
  school_id?: string
  show_my_pending_or_rejected?: boolean
}

export const useQuestions = (filters: QuestionFilters = {}) => {
  const { user, profile, isAdmin, isSchoolAdmin } = useAuth()
  const queryClient = useQueryClient()
  const [totalCount, setTotalCount] = useState(0)

  const {
    data: questions = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['questions', filters, user?.id, isAdmin, isSchoolAdmin, profile?.escola],
    queryFn: async () => {
      let query = supabase
        .from('questions')
        .select('*, profiles(nome)', { count: 'exact' })

      if (filters.disciplina) {
        query = query.eq('disciplina', filters.disciplina)
      }
      if (filters.serie) {
        query = query.eq('serie', filters.serie)
      }
      if (filters.topico) {
        query = query.eq('topico', filters.topico)
      }
      if (filters.dificuldade) {
        query = query.eq('dificuldade', filters.dificuldade)
      }
      if (filters.tipo) {
        query = query.eq('tipo', filters.tipo)
      }
      if (filters.competencia_bncc) {
        query = query.ilike('competencia_bncc', `%${filters.competencia_bncc}%`)
      }
      if (filters.search) {
        // Tentar busca full-text primeiro, fallback para ilike
        try {
          const searchTerm = filters.search.replace(/[^\w\s]/g, ' ').trim()
          if (searchTerm) {
            query = query.or(`search_vector.fts.${searchTerm},enunciado.ilike.%${filters.search}%,topico.ilike.%${filters.search}%,disciplina.ilike.%${filters.search}%`)
          }
        } catch (error) {
          // Fallback para busca tradicional se full-text falhar
          query = query.or(`enunciado.ilike.%${filters.search}%,topico.ilike.%${filters.search}%,disciplina.ilike.%${filters.search}%`)
        }
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags)
      }
      if (filters.is_verified !== undefined) {
        query = query.eq('is_verified', filters.is_verified)
      }
      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after)
      }
      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before)
      }

      if (isAdmin) {
        // Para admins, aplicar filtros de status
        if (filters.status && filters.status !== 'all') {
          query = query.eq('status', filters.status);
        }

        // Se especificou autor, filtrar por autor
        if (filters.autor_id) {
          query = query.eq('autor_id', filters.autor_id);
        }

        // Para admins no wizard (status='approved'), mostrar apenas questões públicas aprovadas
        // Para admins no painel de gestão, podem ver todas
        if (filters.status === 'approved' && !filters.autor_id) {
          query = query.eq('is_public', true);
        }
      } else if (isSchoolAdmin && profile?.escola) {
        // Para school admin, sempre mostrar questões aprovadas + suas próprias + compartilhadas com escola
        if (filters.show_my_pending_or_rejected) {
          query = query.eq('autor_id', user.id).or(`status.eq.pending,status.eq.rejected`);
        } else {
          query = query.or(`autor_id.eq.${user?.id},and(is_public.eq.true,status.eq.approved),and(is_shared_with_school.eq.true,school_id.eq.${profile.escola})`);
        }
      } else if (user) {
        // Para usuários normais, mostrar suas questões + questões públicas aprovadas + compartilhadas com escola
        if (filters.show_my_pending_or_rejected) {
          query = query.eq('autor_id', user.id).or(`status.eq.pending,status.eq.rejected`);
        } else {
          const orConditions = [`autor_id.eq.${user.id}`, `and(is_public.eq.true,status.eq.approved)`];
          if (profile?.escola) {
            orConditions.push(`and(is_shared_with_school.eq.true,school_id.eq.${profile.escola})`);
          }
          query = query.or(orConditions.join(','));
        }
      } else {
        // Para usuários não logados, apenas questões públicas aprovadas
        query = query.eq('is_public', true).eq('status', 'approved');
      }

      if (filters.limit) {
        const page = filters.page || 1
        const from = (page - 1) * filters.limit
        const to = from + filters.limit - 1
        
        query = query.range(from, to)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error, count } = await query

      if (error) {
        console.error('Error fetching questions:', error)
        throw error
      }

      if (count !== null) {
        setTotalCount(count)
      }

      return data as Question[]
    },
    enabled: true, // Sempre habilitado - usuários não logados podem ver questões públicas
    staleTime: 5 * 60 * 1000,
    retry: 1,
    keepPreviousData: true
  })

  const createQuestionMutation = useMutation({
    mutationFn: async (questionData: QuestionInsert) => {
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('questions')
        .insert({
          ...questionData,
          autor_id: user.id,
          source: 'teacher',
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Questão criada com sucesso!')
    },
    onError: (error) => {
      console.error('Error creating question:', error)
      toast.error('Erro ao criar questão')
    }
  })

  const updateQuestionMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: QuestionUpdate }) => {
      const { data, error } = await supabase
        .from('questions')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Questão atualizada com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating question:', error)
      toast.error('Erro ao atualizar questão')
    }
  })

  const deleteQuestionMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('questions')
        .delete()
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Questão excluída com sucesso!')
    },
    onError: (error) => {
      console.error('Error deleting question:', error)
      toast.error('Erro ao excluir questão')
    }
  })

  const approveQuestionMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('questions')
        .update({ status: 'approved' })
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Questão aprovada com sucesso!')
    },
    onError: (error) => {
      console.error('Error approving question:', error)
      toast.error('Erro ao aprovar questão')
    }
  })

  const rejectQuestionMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('questions')
        .update({ status: 'rejected' })
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] })
      toast.success('Questão rejeitada com sucesso!')
    },
    onError: (error) => {
      console.error('Error rejecting question:', error)
      toast.error('Erro ao rejeitar questão')
    }
  })

  return {
    questions,
    totalCount,
    isLoading,
    error,
    refetch,
    createQuestion: createQuestionMutation.mutate,
    updateQuestion: updateQuestionMutation.mutate,
    deleteQuestion: deleteQuestionMutation.mutate,
    approveQuestion: approveQuestionMutation.mutate,
    rejectQuestion: rejectQuestionMutation.mutate,
    isCreating: createQuestionMutation.isPending,
    isUpdating: updateQuestionMutation.isPending,
    isDeleting: deleteQuestionMutation.isPending,
    isApproving: approveQuestionMutation.isPending,
    isRejecting: rejectQuestionMutation.isPending
  }
}

export const useFavorites = () => {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: favorites = [],
    isLoading
  } = useQuery({
    queryKey: ['favorites', user?.id],
    queryFn: async () => {
      if (!user) return []

      const { data, error } = await supabase
        .from('favorites')
        .select(`
          *,
          questions (*)
        `)
        .eq('user_id', user.id)

      if (error) {
        console.error('Error fetching favorites:', error)
        throw error
      }
      
      return data || []
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000,
    retry: 1
  })

  const toggleFavoriteMutation = useMutation({
    mutationFn: async (questionId: string) => {
      if (!user) throw new Error('User not authenticated')

      const { data: existing } = await supabase
        .from('favorites')
        .select('id')
        .eq('user_id', user.id)
        .eq('question_id', questionId)
        .maybeSingle()

      if (existing) {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('id', existing.id)

        if (error) throw error
        return { action: 'removed' }
      } else {
        const { error } = await supabase
          .from('favorites')
          .insert({
            user_id: user.id,
            question_id: questionId
          })

        if (error) throw error
        return { action: 'added' }
      }
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] })
      toast.success(
        result.action === 'added' 
          ? 'Questão adicionada aos favoritos!' 
          : 'Questão removida dos favoritos!'
      )
    },
    onError: (error) => {
      console.error('Error toggling favorite:', error)
      toast.error('Erro ao atualizar favoritos')
    }
  })

  const isFavorite = (questionId: string) => {
    return favorites.some(fav => fav.question_id === questionId)
  }

  return {
    favorites,
    isLoading,
    toggleFavorite: toggleFavoriteMutation.mutate,
    isFavorite,
    isToggling: toggleFavoriteMutation.isPending
  }
}