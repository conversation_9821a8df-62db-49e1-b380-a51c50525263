import React from 'react'
import { motion } from 'framer-motion'
import { BarChart3, TrendingUp, TrendingDown } from 'lucide-react'

interface MonthlyData {
  month: string
  count: number
}

interface MonthlyGrowthChartProps {
  title: string
  data: MonthlyData[]
  color: string
  icon: React.ComponentType<any>
}

const MonthlyGrowthChart: React.FC<MonthlyGrowthChartProps> = ({
  title,
  data,
  color,
  icon: Icon
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Icon className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
        </div>
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          Dados insuficientes para exibir o gráfico
        </div>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(d => d.count))
  const currentMonth = data[data.length - 1]?.count || 0
  const previousMonth = data[data.length - 2]?.count || 0
  const trend = previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Icon className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
        </div>
        <div className="flex items-center space-x-1">
          <span className={`text-sm font-medium ${
            trend > 0 ? 'text-green-600 dark:text-green-400' : 
            trend < 0 ? 'text-red-600 dark:text-red-400' : 
            'text-gray-500 dark:text-gray-400'
          }`}>
            {trend > 0 ? '+' : ''}{trend.toFixed(1)}%
          </span>
          {trend > 0 && <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />}
          {trend < 0 && <TrendingDown className="w-4 h-4 text-red-600 dark:text-red-400" />}
        </div>
      </div>

      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="w-12 text-xs text-gray-500 dark:text-gray-400 font-medium">
              {item.month}
            </div>
            <div className="flex-1 relative">
              <div className="h-6 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: maxValue > 0 ? `${(item.count / maxValue) * 100}%` : '0%' }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className={`h-full ${color} rounded-full`}
                />
              </div>
            </div>
            <div className="w-8 text-xs text-gray-700 dark:text-gray-300 font-medium text-right">
              {item.count}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-between text-sm">
          <span className="text-gray-500 dark:text-gray-400">Total (6 meses)</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {data.reduce((sum, item) => sum + item.count, 0)}
          </span>
        </div>
      </div>
    </motion.div>
  )
}

export default MonthlyGrowthChart
