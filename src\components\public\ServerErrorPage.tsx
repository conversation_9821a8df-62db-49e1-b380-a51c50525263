import React from 'react'
import { <PERSON> } from 'react-router-dom'
import PublicLayout from './PublicLayout'
import PublicBreadcrumbs from './PublicBreadcrumbs'
import SEOHead from './SEOHead'
import { AlertTriangle, Home, RefreshCw, Mail, ArrowLeft } from 'lucide-react'

/**
 * Página de Erro 500 - Erro Interno do Servidor
 */
const ServerErrorPage: React.FC = () => {
  const breadcrumbs = [
    { label: 'Erro do Servidor', href: '/500', current: true }
  ]

  const seoData = {
    title: 'Erro do Servidor | Atividade Pronta',
    description: 'Ocorreu um erro interno no servidor. Nossa equipe foi notificada e está trabalhando para resolver o problema.',
    keywords: ['500', 'erro do servidor', 'atividade pronta', 'erro interno'],
    canonical: 'https://atvpronta.com.br/500',
    ogTitle: 'Erro do Servidor | Atividade Pronta',
    ogDescription: 'Ocorreu um erro interno no servidor.',
    ogImage: 'https://atvpronta.com.br/og-image.jpg',
    ogUrl: 'https://atvpronta.com.br/500',
    noindex: true // Não indexar páginas de erro
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <PublicLayout>
      {/* SEO Head */}
      <SEOHead
        metadata={seoData}
        breadcrumbs={breadcrumbs}
      />

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Breadcrumbs */}
        <PublicBreadcrumbs items={breadcrumbs} className="mb-6" />

        {/* Error Content */}
        <div className="text-center py-16">
          {/* Error Icon */}
          <div className="mb-8">
            <div className="mx-auto w-32 h-32 bg-gradient-to-br from-red-100 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-16 h-16 text-red-600 dark:text-red-400" />
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-4">
              500
            </h1>
            <h2 className="text-3xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Erro Interno do Servidor
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Ops! Algo deu errado em nossos servidores. Nossa equipe técnica foi notificada 
              automaticamente e está trabalhando para resolver o problema o mais rápido possível.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <button
              onClick={handleRefresh}
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
            >
              <RefreshCw className="w-5 h-5 mr-2" />
              Tentar Novamente
            </button>
            
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg border border-gray-300 dark:border-gray-600 transition-colors duration-200"
            >
              <Home className="w-5 h-5 mr-2" />
              Voltar ao Início
            </Link>
          </div>

          {/* Status Information */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mb-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              O que aconteceu?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Erro Temporário
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Este é um problema temporário em nossos servidores. 
                      Geralmente se resolve em poucos minutos.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <RefreshCw className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Equipe Notificada
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Nossa equipe técnica foi automaticamente notificada 
                      e está trabalhando na solução.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Support */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Precisa de Ajuda Urgente?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Se o problema persistir ou for urgente, entre em contato conosco diretamente.
            </p>
            <a
              href="mailto:<EMAIL>?subject=Erro 500 - Problema no Servidor"
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
            >
              <Mail className="w-4 h-4 mr-2" />
              Contatar Suporte
            </a>
          </div>

          {/* Helpful Tips */}
          <div className="text-left bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Enquanto isso, você pode:
            </h3>
            <ul className="space-y-3 text-gray-600 dark:text-gray-400">
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Aguardar alguns minutos e tentar novamente</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Verificar se sua conexão com a internet está funcionando</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Limpar o cache do navegador e tentar novamente</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Explorar outras seções da plataforma que podem estar funcionando</span>
              </li>
            </ul>
          </div>

          {/* Back Button */}
          <div className="mt-8">
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar à página anterior
            </button>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}

export default ServerErrorPage
