import React from 'react'
import { Image as ImageIcon, AlertCircle } from 'lucide-react'
import { AssessmentConfig } from '../../../types/assessment'
import ImageUpload from '../../common/ImageUpload'
import { AssessmentAsset } from '../../../hooks/useAssets'
import SchoolNameSelect from '../../common/SchoolNameSelect'
import { DISCIPLINAS, SERIES } from '../../../constants/educationOptions'

interface HeaderSettingsProps {
  config: AssessmentConfig
  onConfigChange: (config: AssessmentConfig) => void
  isPremium: boolean
  isEscolar: boolean
}

const HeaderSettings: React.FC<HeaderSettingsProps> = ({
  config,
  onConfigChange,
  isPremium,
  isEscolar
}) => {
  const handleHeaderChange = (field: string, value: string) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        [field]: value
      }
    })
  }

  const handleCustomHeaderAssetSelect = (asset: AssessmentAsset | null) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          customHeader: {
            enabled: true,
            asset: asset
          }
        }
      }
    })
  }

  const handleSchoolLogoAssetSelect = (asset: AssessmentAsset | null) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          schoolLogo: {
            enabled: true,
            asset: asset
          }
        }
      }
    })
  }

  const handleCustomHeaderToggle = (enabled: boolean) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          customHeader: {
            enabled,
            asset: config.headerConfig.customization?.customHeader?.asset || null
          }
        }
      }
    })
  }

  const handleSchoolLogoToggle = (enabled: boolean) => {
    onConfigChange({
      ...config,
      headerConfig: {
        ...config.headerConfig,
        customization: {
          ...config.headerConfig.customization,
          schoolLogo: {
            enabled,
            asset: config.headerConfig.customization?.schoolLogo?.asset || null
          }
        }
      }
    })
  }

  const isCustomHeaderEnabled = config.headerConfig.customization?.customHeader?.enabled || false
  const isSchoolLogoEnabled = config.headerConfig.customization?.schoolLogo?.enabled || false

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        Configurações do Cabeçalho
      </h3>

      {/* Informações Básicas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nome da Escola
          </label>
          <SchoolNameSelect
            value={config.headerConfig.nomeEscola}
            onChange={(value) => handleHeaderChange('nomeEscola', value)}
            placeholder="Digite o nome da escola"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nome da Avaliação
          </label>
          <input
            type="text"
            value={config.headerConfig.nomeProva}
            onChange={(e) => handleHeaderChange('nomeProva', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Ex: Avaliação de Matemática"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Disciplina
          </label>
          <select
            value={config.headerConfig.disciplina || ''}
            onChange={(e) => handleHeaderChange('disciplina', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Selecione uma disciplina</option>
            {DISCIPLINAS.map((disciplina) => (
              <option key={disciplina} value={disciplina}>
                {disciplina}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Série
          </label>
          <select
            value={config.headerConfig.serie || ''}
            onChange={(e) => handleHeaderChange('serie', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Selecione uma série</option>
            {SERIES.map((serie) => (
              <option key={serie} value={serie}>
                {serie}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Data
          </label>
          <input
            type="date"
            value={config.headerConfig.data}
            onChange={(e) => handleHeaderChange('data', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Professor(a)
          </label>
          <input
            type="text"
            value={config.headerConfig.professor || ''}
            onChange={(e) => handleHeaderChange('professor', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Nome do professor"
          />
        </div>
      </div>

      {/* Instruções */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Instruções
        </label>
        <textarea
          value={config.headerConfig.instrucoes}
          onChange={(e) => handleHeaderChange('instrucoes', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Instruções para os alunos..."
        />
      </div>

      {/* Customização Premium */}
      {(isPremium || isEscolar) && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Personalização Premium
          </h4>

          {/* Two columns layout for header customization */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Custom Header Section */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">
                    Cabeçalho Personalizado
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Substitui completamente o cabeçalho padrão por uma imagem personalizada
                  </p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isCustomHeaderEnabled}
                    onChange={(e) => handleCustomHeaderToggle(e.target.checked)}
                    className="mr-2 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Ativar</span>
                </label>
              </div>

              {isCustomHeaderEnabled && (
                <ImageUpload
                  assetType="custom_header"
                  selectedAsset={config.headerConfig.customization?.customHeader?.asset}
                  onAssetSelect={handleCustomHeaderAssetSelect}
                  title="Imagem do Cabeçalho"
                  description="Recomendado: 800x200px. Esta imagem substituirá completamente o cabeçalho padrão."
                  maxWidth="400px"
                  maxHeight="100px"
                />
              )}
            </div>

            {/* School Logo Section */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">
                    Logo da Escola
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Adiciona o logo/brasão da escola no cabeçalho padrão
                  </p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isSchoolLogoEnabled}
                    onChange={(e) => handleSchoolLogoToggle(e.target.checked)}
                    className="mr-2 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
                    disabled={isCustomHeaderEnabled}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Ativar</span>
                </label>
              </div>

              {isCustomHeaderEnabled && (
                <div className="flex items-center space-x-2 text-sm text-amber-600 dark:text-amber-400 mb-3">
                  <AlertCircle className="w-4 h-4" />
                  <span>Desabilitado quando cabeçalho personalizado está ativo</span>
                </div>
              )}

              {isSchoolLogoEnabled && !isCustomHeaderEnabled && (
                <ImageUpload
                  assetType="school_logo"
                  selectedAsset={config.headerConfig.customization?.schoolLogo?.asset}
                  onAssetSelect={handleSchoolLogoAssetSelect}
                  title="Logo/Brasão da Escola"
                  description="Recomendado: formato quadrado, 200x200px. Será exibido no canto superior esquerdo."
                  maxWidth="120px"
                  maxHeight="120px"
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default HeaderSettings
