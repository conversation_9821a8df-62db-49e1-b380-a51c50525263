import React from 'react'
import { User, Save, Check } from 'lucide-react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'
import SchoolNameSelect from '../common/SchoolNameSelect'

const profileSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  escola: z.string().min(2, 'Nome da escola deve ter pelo menos 2 caracteres'),
  disciplinas: z.array(z.string()).min(1, 'Selecione pelo menos uma disciplina'),
  series: z.array(z.string()).min(1, 'Selecione pelo menos uma série'),
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileSettingsProps {
  profile: any
  schoolData: any
  savingProfile: boolean
  onSaveProfile: (data: ProfileFormData) => Promise<void>
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({
  profile,
  schoolData,
  savingProfile,
  onSaveProfile
}) => {
  const {
    register: registerProfile,
    handleSubmit: handleSubmitProfile,
    formState: { errors: profileErrors, isValid: isProfileValid },
    watch: watchProfile,
    setValue: setValueProfile,
    reset: resetProfile
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    mode: 'onChange',
    defaultValues: {
      nome: profile?.nome || '',
      email: profile?.email || '',
      escola: schoolData?.name || profile?.escola || '',
      disciplinas: profile?.disciplinas || [],
      series: profile?.series || [],
    }
  })

  // Reset form when profile changes
  React.useEffect(() => {
    if (profile) {
      resetProfile({
        nome: profile.nome || '',
        email: profile.email || '',
        escola: schoolData?.name || profile.escola || '',
        disciplinas: profile.disciplinas || [],
        series: profile.series || [],
      })
    }
  }, [profile, schoolData, resetProfile])

  const watchedDisciplinas = watchProfile('disciplinas')
  const watchedSeries = watchProfile('series')

  const handleDisciplinaChange = (disciplina: string) => {
    const current = watchedDisciplinas || []
    const updated = current.includes(disciplina)
      ? current.filter(d => d !== disciplina)
      : [...current, disciplina]
    setValueProfile('disciplinas', updated)
  }

  const handleSerieChange = (serie: string) => {
    const current = watchedSeries || []
    const updated = current.includes(serie)
      ? current.filter(s => s !== serie)
      : [...current, serie]
    setValueProfile('series', updated)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
          <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Perfil</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Gerencie suas informações pessoais e profissionais
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmitProfile(onSaveProfile)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nome Completo
            </label>
            <input
              {...registerProfile('nome')}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Seu nome completo"
            />
            {profileErrors.nome && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.nome.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email
            </label>
            <input
              {...registerProfile('email')}
              type="email"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="<EMAIL>"
            />
            {profileErrors.email && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.email.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Escola
          </label>
          <SchoolNameSelect
            value={watchProfile('escola')}
            onChange={(value) => setValueProfile('escola', value)}
            placeholder="Nome da sua escola"
          />
          {profileErrors.escola && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.escola.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Disciplinas que Leciona
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {DISCIPLINAS.map((disciplina) => (
              <label key={disciplina} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={watchedDisciplinas?.includes(disciplina) || false}
                  onChange={() => handleDisciplinaChange(disciplina)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">{disciplina}</span>
              </label>
            ))}
          </div>
          {profileErrors.disciplinas && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.disciplinas.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Séries que Leciona
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {SERIES.map((serie) => (
              <label key={serie} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={watchedSeries?.includes(serie) || false}
                  onChange={() => handleSerieChange(serie)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">{serie}</span>
              </label>
            ))}
          </div>
          {profileErrors.series && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{profileErrors.series.message}</p>
          )}
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!isProfileValid || savingProfile}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {savingProfile ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Salvar Perfil</span>
              </>
            )}
          </button>
        </div>
      </form>
    </motion.div>
  )
}

export default ProfileSettings
