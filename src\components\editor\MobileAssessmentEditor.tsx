import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Save, Download, Plus, Search, Filter, Edit, Trash2, HelpCircle, FileText, Type } from 'lucide-react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useQuestions } from '../../hooks/useQuestions'
import { useAssessments } from '../../hooks/useAssessments'
import { useTemplates } from '../../hooks/useTemplates'
import { useSubscription } from '../../contexts/SubscriptionContext'
import { useUsageLimits } from '../../hooks/useUsageLimits'
import { generatePDF, downloadPDF } from '../../utils/pdfGenerator'
import { WATERMARK_CONFIG } from '../../constants/usageLimits'
import QuestionCard from '../questions/QuestionCard'
import AssessmentPreview from './AssessmentPreview'
import AssessmentSettings from './AssessmentSettings'
import QuestionDetailModal from './QuestionDetailModal'
import TextBlockModal from './TextBlockModal'
import CustomQuestionModal from './CustomQuestionModal'
import SaveAsTemplateModal from './SaveAsTemplateModal'
import MobileEditorTabs from './MobileEditorTabs'
import { useIsMobile } from '../../hooks/useIsMobile'
import { Database } from '../../types/database'
import { CreateTemplateDTO } from '../../types/templates'
import { supabase } from '../../lib/supabase'
import toast from 'react-hot-toast'
import { useQuestionSelection } from '../../contexts/QuestionSelectionContext'
import { useAuth } from '../../contexts/AuthContext'
import { useAssets } from '../../hooks/useAssets'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'

type Question = Database['public']['Tables']['questions']['Row']

interface TextBlock {
  id: string;
  type: 'text';
  content: string;
  style?: 'normal' | 'heading' | 'subheading' | 'instruction';
  textAlign?: 'left' | 'center' | 'right' | 'justify';
}

type AssessmentItem = Question | TextBlock;

interface AssessmentConfig {
  titulo: string
  disciplina: string
  serie: string
  headerConfig: {
    nomeEscola: string
    nomeProva: string
    serie: string
    data: string
    instrucoes: string
  }
  pdfOptions: {
    paperSize: 'A4' | 'Letter'
    orientation: 'portrait' | 'landscape'
    fontSize: 'small' | 'medium' | 'large'
    lineSpacing: 'compact' | 'normal' | 'expanded'
    includeAnswerSheet: boolean
    generateVersions: number
    watermark?: string
  }
  showFooter: boolean
}



const MobileAssessmentEditor: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { templates } = useTemplates()
  const { canAccess, isPremium, isEscolar } = useSubscription()
  const { isAdmin, user } = useAuth()
  const { getAssetUrl } = useAssets()
  const {
    checkCanPerformAction,
    trackUsage,
    isPaidUser
  } = useUsageLimits()
  const [activeTab, setActiveTab] = useState<'questions' | 'editor' | 'settings' | 'preview'>('editor')
  const [selectedItems, setSelectedItems] = useState<AssessmentItem[]>([])
  const [showQuestionDetail, setShowQuestionDetail] = useState(false)
  const [showTextBlockModal, setShowTextBlockModal] = useState(false)
  const [showCustomQuestionModal, setShowCustomQuestionModal] = useState(false)
  const [showSaveAsTemplateModal, setShowSaveAsTemplateModal] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [editingTextBlock, setEditingTextBlock] = useState<TextBlock | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [visibleCount, setVisibleCount] = useState(10)
  const [filters, setFilters] = useState({
    disciplina: '',
    serie: '',
    dificuldade: '',
    tipo: ''
  })
  
  const [assessmentConfig, setAssessmentConfig] = useState<AssessmentConfig>({
    titulo: 'Nova Avaliação',
    disciplina: '',
    serie: '',
    headerConfig: {
      nomeEscola: 'Nome da Escola',
      nomeProva: 'Avaliação',
      serie: '',
      data: new Date().toLocaleDateString('pt-BR'),
      instrucoes: 'Leia atentamente cada questão antes de responder.'
    },
    pdfOptions: {
      paperSize: 'A4',
      orientation: 'portrait',
      fontSize: 'medium',
      fontFamily: 'times',
      lineSpacing: 'normal',
      includeAnswerSheet: false,
      generateVersions: 1
    },
    showFooter: true
  })

  const { questions, isLoading } = useQuestions({
    search: searchTerm,
    ...filters,
    status: 'approved' // Apenas questões aprovadas
  })
  const { createAssessment, isCreating } = useAssessments()
  const { selectedIds, clearSelection } = useQuestionSelection()

  // Modal para definir nome da avaliação antes de salvar
  const [showNameModal, setShowNameModal] = useState(false)
  const [tempAssessmentTitle, setTempAssessmentTitle] = useState('')
  const { isMobile } = useIsMobile()

  // useEffect para adicionar questões selecionadas ao montar o editor
  useEffect(() => {
    if (selectedIds.length > 0 && selectedItems.length === 0 && questions.length > 0) {
      const selectedQuestions = questions.filter(q => selectedIds.includes(q.id))
      if (selectedQuestions.length > 0) {
        setSelectedItems(selectedQuestions)
        clearSelection()
      }
    }
  }, [selectedIds, questions, selectedItems.length, clearSelection])

  useEffect(() => {
    setVisibleCount(10)
  }, [searchTerm, filters, questions])

  const addQuestion = (question: Question) => {
    if (!selectedItems.find(item => 'id' in item && item.id === question.id)) {
      // Converter questão do banco para o formato AssessmentItem
      const assessmentQuestion: AssessmentItem = {
        id: question.id,
        enunciado: question.enunciado,
        tipo: question.tipo,
        opcoes: question.alternativas || undefined,
        resposta_correta: question.resposta_correta,
        disciplina: question.disciplina,
        serie: question.serie,
        dificuldade: question.dificuldade === 'Fácil' ? 'facil' :
                     question.dificuldade === 'Médio' ? 'medio' : 'dificil',
        tags: question.tags || [],
        imagem_url: question.imagem_url,
        explicacao: question.explicacao,
        created_at: question.created_at,
        updated_at: question.updated_at,
        autor_id: question.autor_id || '',
        status: question.status,
        metadata: question.metadata
      }

      setSelectedItems([...selectedItems, assessmentQuestion])
      toast.success('Questão adicionada à avaliação')
      setActiveTab('editor') // Muda para aba do editor após adicionar
    } else {
      toast.error('Questão já foi adicionada')
    }
  }

  const removeItem = (index: number) => {
    const newItems = [...selectedItems]
    newItems.splice(index, 1)
    setSelectedItems(newItems)
    toast.success('Item removido da avaliação')
  }

  const handleGeneratePDF = async () => {
    if (selectedItems.length === 0) {
      toast.error('Adicione itens à avaliação antes de gerar o PDF')
      return
    }

    // Check usage limits for free users
    const canDownload = await checkCanPerformAction('download_pdf')
    if (!canDownload) {
      return // Error message already shown by checkCanPerformAction
    }

    try {
      toast.loading('Gerando PDF...', { id: 'pdf-generation' })

      // Preparar configuração do cabeçalho com URLs das imagens (mesmo que AssessmentPreview)
      const customization = assessmentConfig.headerConfig.customization;

      const headerConfigWithUrls = {
        ...assessmentConfig.headerConfig,
        customization: customization ? {
          customHeader: customization.customHeader?.enabled && customization.customHeader.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.customHeader.asset.file_path)
          } : customization.customHeader ? {
            enabled: customization.customHeader.enabled || false
          } : undefined,
          schoolLogo: customization.schoolLogo?.enabled && customization.schoolLogo.asset ? {
            enabled: true,
            imageUrl: getAssetUrl(customization.schoolLogo.asset.file_path)
          } : customization.schoolLogo ? {
            enabled: customization.schoolLogo.enabled || false
          } : undefined
        } : undefined
      };

      const blob = await generatePDF(selectedItems, {
        ...assessmentConfig.pdfOptions,
        headerConfig: headerConfigWithUrls,
        showFooter: isPremium || isEscolar ? assessmentConfig.showFooter : true,
        watermark: isPaidUser ? undefined : WATERMARK_CONFIG.FREE_PLAN_TEXT
      })

      downloadPDF(blob, `${assessmentConfig.titulo}.pdf`)

      // Track PDF download with new system
      await trackUsage('pdf_downloaded', {
        assessment_title: assessmentConfig.titulo,
        questions_count: selectedItems.filter(item => 'tipo' in item && item.tipo !== undefined).length,
        text_blocks_count: selectedItems.length - selectedItems.filter(item => 'tipo' in item && item.tipo !== undefined).length
      })

      toast.success('PDF gerado com sucesso!', { id: 'pdf-generation' })
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Erro ao gerar PDF', { id: 'pdf-generation' })
    }
  }

  const handleSaveAssessment = async () => {
    if (selectedItems.length === 0) {
      toast.error('Adicione pelo menos um item à avaliação')
      return
    }

    // Check usage limits for free users
    const canCreate = await checkCanPerformAction('create_assessment')
    if (!canCreate) {
      return // Error message already shown by checkCanPerformAction
    }

    setTempAssessmentTitle(assessmentConfig.titulo)
    setShowNameModal(true)
  }

  const confirmAndSave = async () => {
    try {
      const questionIds = selectedItems
        .filter(item => 'tipo' in item && item.tipo !== undefined)
        .map(item => (item as Question).id)

      const textBlocks = selectedItems
        .filter(item => 'type' in item && item.type === 'text')
        .map(item => item as TextBlock)

      await createAssessment({
        titulo: tempAssessmentTitle,
        disciplina: assessmentConfig.disciplina || 'Geral',
        serie: assessmentConfig.serie || 'Geral',
        questoes_ids: questionIds,
        configuracao: {
          ...assessmentConfig,
          titulo: tempAssessmentTitle,
          textBlocks
        },
        is_public: isAdmin,
        versoes: 1,
        estatisticas: {},
        metadata: {}
      })

      // Track usage with new system
      await trackUsage('assessment_created', {
        titulo: tempAssessmentTitle,
        disciplina: assessmentConfig.disciplina,
        serie: assessmentConfig.serie,
        questoes_count: questionIds.length,
        text_blocks_count: textBlocks.length
      })

      setSelectedItems([])
      setAssessmentConfig(prev => ({ ...prev, titulo: 'Nova Avaliação' }))
      setShowNameModal(false)

      toast.success('Avaliação salva com sucesso!')
    } catch (error) {
      console.error('Error saving assessment:', error)
      toast.error('Erro ao salvar avaliação')
    }
  }

  const handleAddTextBlock = (textBlock: TextBlock) => {
    if (editingTextBlock) {
      const updatedItems = selectedItems.map(item =>
        'id' in item && item.id === editingTextBlock.id ? { ...item, content: textBlock.content, style: textBlock.style, textAlign: textBlock.textAlign } : item
      );
      setSelectedItems(updatedItems as AssessmentItem[]);
      toast.success('Bloco de texto atualizado!');
    } else {
      setSelectedItems([...selectedItems, textBlock])
      toast.success('Bloco de texto adicionado à avaliação')
    }
    setShowTextBlockModal(false)
    setEditingTextBlock(null)
    setActiveTab('editor') // Muda para aba do editor após adicionar
  }

  const handleInsertCustomQuestion = (customQuestion: any) => {
    // Converter questão customizada para o formato Question (types/assessment.ts)
    const questionItem: AssessmentItem = {
      id: customQuestion.id,
      enunciado: customQuestion.enunciado,
      tipo: customQuestion.tipo,
      opcoes: customQuestion.alternativas || undefined,
      resposta_correta: customQuestion.resposta_correta,
      disciplina: customQuestion.disciplina || 'Personalizada',
      serie: customQuestion.serie || 'Geral',
      dificuldade: customQuestion.dificuldade === 'Fácil' ? 'facil' :
                   customQuestion.dificuldade === 'Médio' ? 'medio' : 'dificil',
      tags: customQuestion.tags || [],
      imagem_url: null,
      explicacao: 'Questão criada pelo professor',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      autor_id: user?.id || '',
      status: 'approved' as const,
      // Adicionar metadata para linhas personalizadas em questões dissertativas
      ...(customQuestion.linhas_resposta && {
        metadata: { linhas_resposta: customQuestion.linhas_resposta }
      })
    }

    setSelectedItems([...selectedItems, questionItem])
    toast.success('Questão personalizada adicionada à avaliação')
    setShowCustomQuestionModal(false)
    setActiveTab('editor') // Muda para aba do editor após adicionar
  }

  const handleInsertAndSaveCustomQuestion = async (customQuestion: any, questionData: any) => {
    try {
      // Primeiro salvar no banco
      const { data, error } = await supabase
        .from('questions')
        .insert(questionData)
        .select()
        .single()

      if (error) throw error

      // Depois adicionar à avaliação usando os dados salvos
      const questionItem: AssessmentItem = {
        ...data,
        metadata: customQuestion.linhas_resposta ? { linhas_resposta: customQuestion.linhas_resposta } : data.metadata
      }

      setSelectedItems([...selectedItems, questionItem])
      toast.success('Questão salva no banco e adicionada à avaliação')
      setShowCustomQuestionModal(false)
      setActiveTab('editor') // Muda para aba do editor após adicionar
    } catch (error) {
      console.error('Erro ao salvar questão:', error)
      toast.error('Erro ao salvar questão no banco de dados')
    }
  }

  const handleViewQuestionDetail = (question: Question) => {
    setSelectedQuestion(question)
    setShowQuestionDetail(true)
  }

  const handleSaveAsTemplate = async (templateData: CreateTemplateDTO) => {
    try {
      const { data: user } = await supabase.auth.getUser()

      const insertData = {
        nome: templateData.nome,
        categoria: templateData.categoria,
        descricao: templateData.descricao || null,
        content: templateData.content,
        metadata: {
          ...templateData.metadata,
          createdAt: new Date().toISOString()
        },
        layout_config: templateData.content.layout, // Backward compatibility
        is_premium: templateData.is_premium || false,
        is_system: templateData.is_system || false,
        autor_id: user.user?.id || null,
        tags: templateData.tags || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { error } = await supabase
        .from('templates')
        .insert(insertData)

      if (error) throw error

      toast.success('Template salvo com sucesso!')
      setShowSaveAsTemplateModal(false)
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Erro ao salvar template')
    }
  }

  const filteredQuestions = questions.filter(question => 
    !selectedItems.some(item => 'id' in item && item.id === question.id)
  )

  // Count questions (excluding text blocks)
  const questionCount = selectedItems.filter(item => 'tipo' in item).length

  // Check if the currently selected question in the modal is already in the assessment
  const isSelectedQuestionInAssessment = selectedQuestion ? selectedItems.some(item => 'id' in item && item.id === selectedQuestion.id) : false;

  const tabVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'questions':
        return (
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Banco de Questões
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Busque e adicione questões à sua avaliação
                </p>
                
                {/* Barra de busca otimizada para mobile */}
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="search"
                    placeholder="Buscar questões..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                {/* Filtros em chips horizontais */}
                <div className="space-y-3">
                  <div className="flex space-x-2 overflow-x-auto pb-2">
                    <button
                      onClick={() => setFilters(prev => ({ ...prev, disciplina: '' }))}
                      className={`flex-shrink-0 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                        !filters.disciplina 
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      Todas
                    </button>
                    {DISCIPLINAS.slice(0, 6).map((disciplina) => (
                      <button
                        key={disciplina}
                        onClick={() => setFilters(prev => ({ 
                          ...prev, 
                          disciplina: prev.disciplina === disciplina ? '' : disciplina 
                        }))}
                        className={`flex-shrink-0 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                          filters.disciplina === disciplina 
                            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {disciplina}
                      </button>
                    ))}
                  </div>
                  
                  {/* Filtros adicionais */}
                  <div className="grid grid-cols-2 gap-2">
                    <select
                      value={filters.serie}
                      onChange={(e) => setFilters(prev => ({ ...prev, serie: e.target.value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Série</option>
                      {SERIES.slice(0, 8).map((serie) => (
                        <option key={serie} value={serie}>{serie}</option>
                      ))}
                    </select>
                    
                    <select
                      value={filters.dificuldade}
                      onChange={(e) => setFilters(prev => ({ ...prev, dificuldade: e.target.value }))}
                      className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Dificuldade</option>
                      <option value="Fácil">Fácil</option>
                      <option value="Médio">Médio</option>
                      <option value="Difícil">Difícil</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Lista de questões */}
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="w-6 h-6 border-2 border-blue-600 dark:border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Carregando questões...</p>
                </div>
              ) : filteredQuestions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Nenhuma questão encontrada</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredQuestions.slice(0, visibleCount).map((question) => (
                    <div key={question.id} className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-start mb-3">
                        <span className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                          {question.tipo === 'multipla_escolha' ? 'Múltipla Escolha' : 
                           question.tipo === 'dissertativa' ? 'Dissertativa' : 
                           'Verdadeiro/Falso'}
                        </span>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewQuestionDetail(question)}
                            className="text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 p-1"
                          >
                            <Search className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => addQuestion(question)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-colors"
                          >
                            Adicionar
                          </button>
                        </div>
                      </div>
                      
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {question.enunciado}
                      </h4>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>{question.disciplina} • {question.serie}</span>
                        <span className={`px-2 py-1 rounded-full ${
                          question.dificuldade === 'Fácil' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' :
                          question.dificuldade === 'Médio' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {question.dificuldade}
                        </span>
                      </div>
                    </div>
                  ))}
                  
                  {/* Botão Carregar Mais */}
                  {filteredQuestions.length > visibleCount && (
                    <button
                      onClick={() => setVisibleCount(prev => prev + 10)}
                      className="w-full mt-4 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 font-medium transition-colors"
                    >
                      Carregar mais questões
                    </button>
                  )}
                </div>
              )}


            </div>
          </div>
        )

      case 'editor':
        return (
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="space-y-4">
              {/* Editor de título otimizado */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Título da Avaliação
                </label>
                <input
                  type="text"
                  value={assessmentConfig.titulo}
                  onChange={(e) => setAssessmentConfig(prev => ({ ...prev, titulo: e.target.value }))}
                  className="w-full px-4 py-3 text-lg font-semibold border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Digite o título da avaliação..."
                />
              </div>

              {/* Lista de questões selecionadas */}
              {selectedItems.length === 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-xl p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 text-center">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Plus className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Avaliação vazia
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Adicione questões para começar
                  </p>
                  <div className="space-y-3">
                    <button
                      onClick={() => setActiveTab('questions')}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium w-full flex items-center justify-center space-x-2"
                    >
                      <HelpCircle className="w-5 h-5" />
                      <span>Buscar Questões</span>
                    </button>
                    <button
                      onClick={() => setShowTextBlockModal(true)}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium w-full flex items-center justify-center space-x-2"
                    >
                      <Type className="w-5 h-5" />
                      <span>Adicionar Texto</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedItems.map((item, index) => (
                    <div key={'id' in item ? item.id : item.id} className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          {'type' in item && item.type === 'text' ? (
                            <div>
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/30 px-2 py-1 rounded">
                                  Bloco de Texto
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {item.style === 'heading' ? 'Título' : 
                                   item.style === 'subheading' ? 'Subtítulo' : 
                                   item.style === 'instruction' ? 'Instrução' : 'Texto Normal'}
                                </span>
                              </div>
                              <p className={`text-gray-900 dark:text-white line-clamp-3 ${
                                item.style === 'heading' ? 'text-lg font-bold' : 
                                item.style === 'subheading' ? 'text-base font-semibold' : 
                                item.style === 'instruction' ? 'text-sm italic' : 'text-sm'
                              }`}>
                                {item.content}
                              </p>
                            </div>
                          ) : (
                            <div>
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                                  {(item as Question).tipo === 'multipla_escolha' ? 'Múltipla Escolha' : 
                                   (item as Question).tipo === 'dissertativa' ? 'Dissertativa' : 
                                   'Verdadeiro/Falso'}
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  Questão {index + 1}
                                </span>
                              </div>
                              <h4 className="font-medium text-gray-900 dark:text-white mb-1 line-clamp-2">
                                {(item as Question).enunciado}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {(item as Question).disciplina} • {(item as Question).serie} • {(item as Question).dificuldade}
                              </p>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-1 ml-3">
                          {'type' in item && item.type === 'text' && (
                            <button
                              onClick={() => {
                                setEditingTextBlock(item as TextBlock)
                                setShowTextBlockModal(true)
                              }}
                              className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded transition-colors"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => removeItem(index)}
                            className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Botões flutuantes quando há itens */}
              {selectedItems.length > 0 && (
                <div className="fixed bottom-24 right-4 flex flex-col space-y-3 z-40">
                  <button
                    onClick={() => setActiveTab('questions')}
                    className="w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
                    title="Adicionar Questão"
                  >
                    <HelpCircle className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => setShowTextBlockModal(true)}
                    className="w-14 h-14 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
                    title="Adicionar Texto"
                  >
                    <Type className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => setShowCustomQuestionModal(true)}
                    className="w-14 h-14 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
                    title="Nova Questão"
                  >
                    <Plus className="w-6 h-6" />
                  </button>
                </div>
              )}
            </div>
          </div>
        )

      case 'settings':
        return (
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="space-y-4">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Configurações
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Disciplina
                    </label>
                    <select className="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                      <option>Selecionar disciplina</option>
                      <option>Matemática</option>
                      <option>Português</option>
                      <option>Ciências</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Série/Ano
                    </label>
                    <select className="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                      <option>Selecionar série</option>
                      <option>5º Ano EF</option>
                      <option>6º Ano EF</option>
                      <option>7º Ano EF</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 'preview':
        return (
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Preview da Avaliação
              </h3>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                  <p className="text-gray-600 dark:text-gray-400">
                    Preview será exibido aqui
                  </p>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header Mobile */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {assessmentConfig.titulo}
          </h1>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSaveAssessment}
            disabled={isCreating}
            className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
          >
            <Save className="w-5 h-5" />
          </button>

          <button
            onClick={() => setShowSaveAsTemplateModal(true)}
            disabled={selectedItems.length === 0}
            className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
            title="Salvar como Modelo"
          >
            <FileText className="w-5 h-5" />
          </button>
          <button 
            onClick={handleGeneratePDF}
            className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
          >
            <Download className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Conteúdo da aba ativa */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          custom={1}
          variants={tabVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: "spring", stiffness: 300, damping: 30 },
            opacity: { duration: 0.2 }
          }}
          className="flex-1 overflow-hidden"
        >
          {renderTabContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navegação por abas */}
      <MobileEditorTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        questionCount={questionCount}
        hasUnsavedChanges={false}
        showQuestionsTab={selectedItems.length > 0}
      />

      {/* Modais */}
      <QuestionDetailModal
        isOpen={showQuestionDetail}
        onClose={() => setShowQuestionDetail(false)}
        question={selectedQuestion}
        onAddToAssessment={selectedQuestion ? () => addQuestion(selectedQuestion) : undefined}
        isQuestionInAssessment={isSelectedQuestionInAssessment}
        onRemoveFromAssessment={selectedQuestion ? () => {
          const indexToRemove = selectedItems.findIndex(item => 'id' in item && item.id === selectedQuestion.id);
          if (indexToRemove !== -1) {
            removeItem(indexToRemove);
            setShowQuestionDetail(false);
          }
        } : undefined}
      />

      <TextBlockModal
        isOpen={showTextBlockModal}
        onClose={() => {
          setShowTextBlockModal(false)
          setEditingTextBlock(null)
        }}
        onSave={handleAddTextBlock}
        initialData={editingTextBlock}
      />

      <CustomQuestionModal
        isOpen={showCustomQuestionModal}
        onClose={() => setShowCustomQuestionModal(false)}
        onInsertOnly={handleInsertCustomQuestion}
        onInsertAndSave={handleInsertAndSaveCustomQuestion}
      />

      <AssessmentSettings
        isOpen={activeTab === 'settings'}
        config={assessmentConfig}
        onConfigChange={setAssessmentConfig}
        onClose={() => setActiveTab('editor')}
      />

      {activeTab === 'preview' && (
        <div className="fixed inset-0 bg-white dark:bg-gray-800 z-50 flex flex-col">
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Preview</h2>
            <button
              onClick={() => setActiveTab('editor')}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
          <div className="flex-1 overflow-hidden">
            <AssessmentPreview
              items={selectedItems}
              config={{ 
                ...assessmentConfig, 
                showFooter: isPremium || isEscolar ? assessmentConfig.showFooter : true 
              }}
              onClose={() => setActiveTab('editor')}
            />
          </div>
        </div>
      )}

      {/* Modal de confirmação para salvar */}
      {showNameModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }} 
            animate={{ opacity: 1, scale: 1 }} 
            exit={{ opacity: 0, scale: 0.9 }} 
            className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md"
          >
            <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Definir Nome da Avaliação
            </h2>
            <input
              type="text"
              value={tempAssessmentTitle}
              onChange={(e) => setTempAssessmentTitle(e.target.value)}
              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg p-3 mb-4 dark:bg-gray-700 dark:text-white text-base"
              placeholder="Ex: Avaliação de Matemática - 2º Bimestre"
            />
            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => setShowNameModal(false)} 
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancelar
              </button>
              <button 
                onClick={confirmAndSave} 
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 transition-colors" 
                disabled={!tempAssessmentTitle.trim() || isCreating}
              >
                {isCreating ? 'Salvando...' : 'Salvar'}
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Save As Template Modal */}
      <SaveAsTemplateModal
        isOpen={showSaveAsTemplateModal}
        onClose={() => setShowSaveAsTemplateModal(false)}
        onSave={handleSaveAsTemplate}
        config={assessmentConfig}
        selectedItems={selectedItems}
      />
    </div>
  )
}

export default MobileAssessmentEditor 