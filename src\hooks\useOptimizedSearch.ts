import { useState, useCallback, useRef, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'

interface SearchOptions {
  table: string
  searchFields: string[]
  selectFields?: string
  filters?: Record<string, any>
  debounceMs?: number
  minSearchLength?: number
  enableFullText?: boolean
  cacheTime?: number
  staleTime?: number
}

interface SearchResult<T> {
  data: T[]
  isLoading: boolean
  error: Error | null
  totalCount: number
  searchTerm: string
  setSearchTerm: (term: string) => void
  clearSearch: () => void
}

/**
 * Hook otimizado para busca com full-text search, debounce e cache inteligente
 */
export function useOptimizedSearch<T = any>(options: SearchOptions): SearchResult<T> {
  const {
    table,
    searchFields,
    selectFields = '*',
    filters = {},
    debounceMs = 300,
    minSearchLength = 2,
    enableFullText = true,
    cacheTime = 5 * 60 * 1000, // 5 minutos
    staleTime = 2 * 60 * 1000   // 2 minutos
  } = options

  const [searchTerm, setSearchTermState] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Debounced search term update
  const setSearchTerm = useCallback((term: string) => {
    setSearchTermState(term)

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(term)
    }, debounceMs)
  }, [debounceMs])

  const clearSearch = useCallback(() => {
    setSearchTermState('')
    setDebouncedSearchTerm('')
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
  }, [])

  // Memoized query key for better cache management
  const queryKey = useMemo(() => [
    'optimized-search',
    table,
    debouncedSearchTerm,
    searchFields,
    filters,
    selectFields
  ], [table, debouncedSearchTerm, searchFields, filters, selectFields])

  // Search query function
  const searchQuery = useCallback(async () => {
    if (!debouncedSearchTerm || debouncedSearchTerm.length < minSearchLength) {
      // Return empty results for short search terms
      return { data: [], count: 0 }
    }

    let query = supabase
      .from(table)
      .select(selectFields, { count: 'exact' })

    // Apply filters first
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          query = query.in(key, value)
        } else {
          query = query.eq(key, value)
        }
      }
    })

    // Apply search
    if (enableFullText && searchFields.length > 0) {
      // Use PostgreSQL full-text search for better performance
      const searchConditions = searchFields.map(field => {
        // For full-text search, we'll use plainto_tsquery for better user experience
        return `${field}.fts.${debouncedSearchTerm.replace(/[^\w\s]/g, ' ').trim()}`
      })
      
      // Fallback to ilike if full-text search is not available
      const ilikeConditions = searchFields.map(field => 
        `${field}.ilike.%${debouncedSearchTerm}%`
      )

      try {
        // Try full-text search first
        const fullTextQuery = query.or(searchConditions.join(','))
        const { data: ftData, error: ftError, count: ftCount } = await fullTextQuery
        
        if (!ftError && ftData) {
          return { data: ftData, count: ftCount || 0 }
        }
      } catch (error) {
        console.warn('Full-text search failed, falling back to ilike:', error)
      }

      // Fallback to ilike search
      query = query.or(ilikeConditions.join(','))
    } else {
      // Use simple ilike search
      const ilikeConditions = searchFields.map(field => 
        `${field}.ilike.%${debouncedSearchTerm}%`
      )
      query = query.or(ilikeConditions.join(','))
    }

    const { data, error, count } = await query
    
    if (error) {
      throw error
    }

    return { data: data || [], count: count || 0 }
  }, [
    table,
    selectFields,
    debouncedSearchTerm,
    searchFields,
    filters,
    minSearchLength,
    enableFullText
  ])

  // React Query with optimized caching
  const {
    data: queryResult,
    isLoading,
    error
  } = useQuery({
    queryKey,
    queryFn: searchQuery,
    enabled: debouncedSearchTerm.length >= minSearchLength,
    staleTime,
    gcTime: cacheTime, // Updated from cacheTime to gcTime for newer React Query versions
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on client errors
      if (error?.message?.includes('400') || error?.message?.includes('401')) {
        return false
      }
      return failureCount < 2
    }
  })

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  return {
    data: queryResult?.data || [],
    isLoading: isLoading && debouncedSearchTerm.length >= minSearchLength,
    error: error as Error | null,
    totalCount: queryResult?.count || 0,
    searchTerm,
    setSearchTerm,
    clearSearch
  }
}

/**
 * Hook especializado para busca de questões
 */
export function useQuestionSearch(filters: Record<string, any> = {}) {
  return useOptimizedSearch({
    table: 'questions',
    searchFields: ['enunciado', 'topico', 'disciplina', 'tags'],
    selectFields: '*, profiles(nome)',
    filters,
    enableFullText: true,
    minSearchLength: 2
  })
}

/**
 * Hook especializado para busca de avaliações
 */
export function useAssessmentSearch(filters: Record<string, any> = {}) {
  return useOptimizedSearch({
    table: 'assessments',
    searchFields: ['titulo', 'disciplina', 'serie'],
    selectFields: '*, profiles(nome, email)',
    filters,
    enableFullText: true,
    minSearchLength: 2
  })
}

/**
 * Hook especializado para busca de templates
 */
export function useTemplateSearch(filters: Record<string, any> = {}) {
  return useOptimizedSearch({
    table: 'templates',
    searchFields: ['nome', 'categoria', 'descricao'],
    selectFields: '*, profiles(nome, email)',
    filters,
    enableFullText: true,
    minSearchLength: 2
  })
}

/**
 * Hook especializado para busca de usuários (admin)
 */
export function useUserSearch(filters: Record<string, any> = {}) {
  return useOptimizedSearch({
    table: 'profiles',
    searchFields: ['nome', 'email', 'escola'],
    selectFields: '*',
    filters,
    enableFullText: false, // Profiles might not have full-text indexes
    minSearchLength: 2
  })
}
