import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import FilterHeader from './filters/FilterHeader'
import SubjectFilter from './filters/SubjectFilter'
import DifficultyFilter from './filters/DifficultyFilter'
import AdvancedFilters from './filters/AdvancedFilters'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected';
  status?: 'pending' | 'approved' | 'rejected' | 'all';
}

interface FilterPanelProps {
  filters: QuestionFilters
  onFilterChange: (key: keyof QuestionFilters, value: string | string[] | boolean) => void
  onClearFilters: () => void
  hasActiveFilters: boolean
  isAdmin: boolean;
  isSchoolAdmin: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
  isAdmin,
  isSchoolAdmin,
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  return (
    <div className="space-y-4">
      <FilterHeader
        filters={filters}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
      />

      {/* Main Filters - Organized Layout */}
      <div className="space-y-4 mb-4">
        <SubjectFilter
          filters={filters}
          onFilterChange={onFilterChange}
        />
        <DifficultyFilter
          filters={filters}
          onFilterChange={onFilterChange}
        />
      </div>

      {/* Advanced Filters Toggle */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center justify-between w-full text-left text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors py-2 px-3 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20"
          aria-expanded={showAdvancedFilters}
          aria-controls="advanced-filters"
        >
          <span className="text-sm font-medium">Filtros Avançados</span>
          {showAdvancedFilters ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>

        <AnimatePresence>
          {showAdvancedFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden mt-3"
              id="advanced-filters"
            >
              <AdvancedFilters
                filters={filters}
                onFilterChange={onFilterChange}
                isAdmin={isAdmin}
                isSchoolAdmin={isSchoolAdmin}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default FilterPanel