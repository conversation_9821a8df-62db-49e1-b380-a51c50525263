import { useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

export interface AuditLogEntry {
  id?: string
  admin_user_id: string
  action_type: string
  target_table?: string
  target_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  description?: string
  created_at?: string
}

export interface AuditLogFilter {
  action_type?: string
  target_table?: string
  admin_user_id?: string
  start_date?: string
  end_date?: string
  limit?: number
}

export const useAuditLog = () => {
  const { user } = useAuth()

  // Função para registrar uma ação no log de auditoria
  const logAction = useCallback(async (
    actionType: string,
    targetTable?: string,
    description?: string,
    targetId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Promise<boolean> => {
    if (!user) {
      console.warn('Cannot log action: user not authenticated')
      return false
    }

    try {
      const logEntry: Omit<AuditLogEntry, 'id'> = {
        admin_user_id: user.id,
        action_type: actionType,
        target_table: targetTable,
        target_id: targetId,
        old_values: oldValues,
        new_values: newValues,
        description: description,
        created_at: new Date().toISOString()
      }

      // Inserir no banco de dados
      const { error } = await supabase
        .from('admin_audit_log')
        .insert([logEntry])

      if (error) {
        console.error('Failed to log audit action:', error)
        return false
      }

      console.log(`Audit log recorded: ${actionType} on ${targetTable}`, description)
      return true
    } catch (error) {
      console.error('Error logging audit action:', error)
      return false
    }
  }, [user])

  // Função para registrar geração de questões
  const logQuestionGeneration = useCallback(async (
    params: Record<string, any>,
    result: { success: boolean; count?: number; error?: string; duration_ms?: number; method?: string }
  ) => {
    const description = `Geração de ${result.count || 0} questões de ${params.disciplina} - ${params.serie}`
    const newValues = {
      generation_params: {
        disciplina: params.disciplina,
        serie: params.serie,
        topico: params.topico,
        subtopico: params.subtopico,
        dificuldade: params.dificuldade,
        tipo: params.tipo,
        quantidade: params.quantidade,
        competencia_bncc: params.competencia_bncc
      },
      result: {
        success: result.success,
        count: result.count || 0,
        method: result.method || 'edge_function',
        error: result.error,
        duration_ms: result.duration_ms
      },
      performance: {
        generation_time: result.duration_ms,
        questions_per_second: result.count && result.duration_ms
          ? (result.count / (result.duration_ms / 1000)).toFixed(2)
          : null,
        efficiency_score: result.count && result.duration_ms
          ? Math.min(100, (result.count / (result.duration_ms / 1000)) * 10)
          : null
      }
    }

    return await logAction(
      'GENERATE_QUESTIONS',
      'questions',
      description,
      undefined,
      undefined,
      newValues
    )
  }, [logAction])

  // Função para registrar aprovação/rejeição de questões
  const logQuestionApproval = useCallback(async (
    questionId: string,
    action: 'APPROVE' | 'REJECT',
    questionData?: Record<string, any>
  ) => {
    const description = `${action === 'APPROVE' ? 'Aprovação' : 'Rejeição'} de questão`
    const newValues = {
      question_data: questionData,
      approval_action: action
    }

    return await logAction(
      `QUESTION_${action}`,
      'questions',
      description,
      questionId,
      undefined,
      newValues
    )
  }, [logAction])

  // Função para registrar regeneração de questões
  const logQuestionRegeneration = useCallback(async (
    questionId: string,
    originalData: Record<string, any>,
    newData: Record<string, any>
  ) => {
    const description = 'Regeneração de questão por solicitação administrativa'

    return await logAction(
      'REGENERATE_QUESTION',
      'questions',
      description,
      questionId,
      originalData,
      newData
    )
  }, [logAction])

  // Função para registrar exportação de questões
  const logQuestionExport = useCallback(async (
    exportFormat: string,
    questionCount: number,
    filters?: Record<string, any>
  ) => {
    const description = `Exportação de ${questionCount} questões em formato ${exportFormat}`
    const newValues = {
      export_format: exportFormat,
      question_count: questionCount,
      export_filters: filters,
      export_timestamp: new Date().toISOString()
    }

    return await logAction(
      'EXPORT_QUESTIONS',
      'questions',
      description,
      undefined,
      undefined,
      newValues
    )
  }, [logAction])

  // Função para registrar acesso ao painel administrativo
  const logAdminAccess = useCallback(async (
    panel: string,
    accessType: 'VIEW' | 'EDIT' | 'DELETE' = 'VIEW'
  ) => {
    const description = `Acesso ${accessType} ao painel ${panel}`
    const newValues = {
      panel_accessed: panel,
      access_type: accessType,
      access_timestamp: new Date().toISOString()
    }

    return await logAction(
      `ADMIN_${accessType}`,
      'admin_panel',
      description,
      undefined,
      undefined,
      newValues
    )
  }, [logAction])

  // Função para buscar logs de auditoria (apenas para admins)
  const getAuditLogs = useCallback(async (filters: AuditLogFilter = {}) => {
    try {
      let query = supabase
        .from('admin_audit_log')
        .select(`
          *,
          profiles:admin_user_id (
            nome,
            email
          )
        `)
        .order('created_at', { ascending: false })

      // Aplicar filtros
      if (filters.action_type) {
        query = query.eq('action_type', filters.action_type)
      }
      if (filters.target_table) {
        query = query.eq('target_table', filters.target_table)
      }
      if (filters.admin_user_id) {
        query = query.eq('admin_user_id', filters.admin_user_id)
      }
      if (filters.start_date) {
        query = query.gte('created_at', filters.start_date)
      }
      if (filters.end_date) {
        query = query.lte('created_at', filters.end_date)
      }

      // Limitar resultados
      const limit = filters.limit || 100
      query = query.limit(limit)

      const { data, error } = await query

      if (error) {
        console.error('Failed to fetch audit logs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      return []
    }
  }, [])

  return {
    logAction,
    logQuestionGeneration,
    logQuestionApproval,
    logQuestionRegeneration,
    logQuestionExport,
    logAdminAccess,
    getAuditLogs
  }
}
