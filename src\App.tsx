import React from 'react'
import { <PERSON>rowser<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from './contexts/AuthContext'
import { SubscriptionProvider } from './contexts/SubscriptionContext'
import { ThemeProvider } from './contexts/ThemeContext'
// DESATIVADO: import { ServiceWorkerNotifications } from './components/common/ServiceWorkerNotifications'
// DESATIVADO: import { PerformanceMonitor } from './components/common/PerformanceMonitor'
import ProtectedRoute from './components/auth/ProtectedRoute'
import AdminProtectedRoute from './components/auth/AdminProtectedRoute'
import ErrorBoundary from './components/common/ErrorBoundary'
import MainLayout from './components/layout/MainLayout'
import AdminLayout from './components/admin/AdminLayout'
import Dashboard from './components/dashboard/Dashboard'
import QuestionBank from './components/questions/QuestionBank'
import AssessmentEditor from './components/editor/AssessmentEditor'
import AssessmentWizard from './components/wizard/AssessmentWizard'
import MyAssessments from './components/assessments/MyAssessments'
import Templates from './components/templates/Templates'
import Analytics from './components/analytics/Analytics'
import Settings from './components/settings/Settings'
import Billing from './components/billing/Billing'
import Collaboration from './components/collaboration/Collaboration'
import Reports from './components/reports/Reports'
import HelpCenter from './components/help/HelpCenter'
import AdminOverview from './components/admin/AdminOverview'
import UserManagement from './components/admin/UserManagement'
import QuestionManagement from './components/admin/QuestionManagement'
import UnifiedFeedbackManagement from './components/admin/UnifiedFeedbackManagement'
import BulkQuestionGenerator from './components/admin/BulkQuestionGenerator'
import AssessmentManagement from './components/admin/AssessmentManagement'
import PublicAssessmentManagement from './components/admin/PublicAssessmentManagement'
import TemplateManagement from './components/admin/TemplateManagement'
import SubscriptionOverview from './components/admin/SubscriptionOverview'
import AdminAnalytics from './components/admin/AdminAnalytics'
import SystemSettings from './components/admin/SystemSettings'
import SEOManagement from './components/admin/SEOManagement'
import NotificationManagement from './components/admin/NotificationManagement'
import SchoolManagement from './components/admin/SchoolManagement'
import PlanManagement from './components/admin/PlanManagement'
import AIProviderSettings from './components/admin/AIProviderSettings'
import LandingPage from './components/landing/LandingPage'
import AuthPage from './components/auth/AuthPage'
import PublicAssessmentList from './components/public/PublicAssessmentList'
import PublicAssessmentDetail from './components/public/PublicAssessmentDetail'
import PublicCategoryPage from './components/public/PublicCategoryPage'
import TermsPage from './components/public/TermsPage'
import PrivacyPage from './components/public/PrivacyPage'
import CookiesPage from './components/public/CookiesPage'
import NotFoundPage from './components/public/NotFoundPage'
import ServerErrorPage from './components/public/ServerErrorPage'
import SitemapRoute from './components/seo/SitemapRoute'
import RobotsRoute from './components/seo/RobotsRoute'
import SchoolAdminProtectedRoute from './components/auth/SchoolAdminProtectedRoute'
import AcceptInvitePage from './components/auth/AcceptInvitePage'
import * as ReactHelmetAsync from 'react-helmet-async'
const { HelmetProvider } = ReactHelmetAsync
import { QuestionSelectionProvider } from './contexts/QuestionSelectionContext'

// DESATIVADO: Create intelligent query client
// import { createIntelligentQueryClient } from './lib/cache/cacheConfig'

// const queryClient = createIntelligentQueryClient()

// Usar QueryClient padrão sem cache inteligente
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
})

function App() {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <Router>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <SubscriptionProvider>
                <ThemeProvider>
                  <QuestionSelectionProvider>
                    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
                      <Toaster
                        position="top-right"
                        toastOptions={{
                          duration: 4000,
                          style: {
                            background: '#363636',
                            color: '#fff',
                          },
                          success: {
                            duration: 3000,
                            iconTheme: {
                              primary: '#4ade80',
                              secondary: '#fff',
                            },
                          },
                          error: {
                            duration: 4000,
                            iconTheme: {
                              primary: '#ef4444',
                              secondary: '#fff',
                            },
                          },
                        }}
                      />

                      {/* DESATIVADO: Performance and Service Worker components */}
                      {/* <ServiceWorkerNotifications /> */}
                      {/* <PerformanceMonitor /> */}

                      <Routes>
                        {/* Landing Page */}
                        <Route path="/" element={<LandingPage />} />

                        {/* SEO Routes */}
                        <Route path="/sitemap.xml" element={<SitemapRoute />} />
                        <Route path="/robots.txt" element={<RobotsRoute />} />

                        {/* Public Assessment Routes */}
                        <Route path="/avaliacoes" element={<PublicAssessmentList />} />
                        <Route path="/avaliacoes/:slug" element={<PublicAssessmentDetail />} />
                        <Route path="/avaliacoes/categoria/:categorySlug" element={<PublicCategoryPage />} />

                        {/* Static Pages */}
                        <Route path="/terms" element={<TermsPage />} />
                        <Route path="/privacy" element={<PrivacyPage />} />
                        <Route path="/cookies" element={<CookiesPage />} />

                        {/* Error Pages */}
                        <Route path="/404" element={<NotFoundPage />} />
                        <Route path="/500" element={<ServerErrorPage />} />

                        {/* Auth Pages */}
                        <Route path="/login" element={<AuthPage />} />
                        <Route path="/register" element={<AuthPage />} />
                        <Route path="/accept-invite" element={<AcceptInvitePage />} />
                        
                        {/* Main Application Routes */}
                        <Route path="/app/*" element={
                          <ProtectedRoute>
                            <Routes>
                              <Route path="/" element={<MainLayout />}>
                                <Route index element={<Dashboard />} />
                                <Route path="questions" element={<QuestionBank />} />
                                <Route path="editor" element={<AssessmentEditor />} />
                                <Route path="wizard" element={<AssessmentWizard />} />
                                <Route path="assessments" element={<MyAssessments />} />
                                <Route path="templates" element={<Templates />} />
                                <Route path="analytics" element={<Analytics />} />
                                <Route path="collaboration" element={<Collaboration />} />
                                <Route path="reports" element={<Reports />} />
                                <Route path="settings" element={<Settings />} />
                                <Route path="billing" element={<Billing />} />
                                <Route path="help" element={<HelpCenter />} />
                                <Route path="school-admin" element={
                                  <SchoolAdminProtectedRoute>
                                    <SchoolManagement />
                                  </SchoolAdminProtectedRoute>
                                } />
                              </Route>
                            </Routes>
                          </ProtectedRoute>
                        } />

                        {/* Admin Panel Routes */}
                        <Route path="/admin/*" element={
                          <ProtectedRoute>
                            <AdminProtectedRoute>
                              <Routes>
                                <Route path="/" element={<AdminLayout />}>
                                  <Route index element={<AdminOverview />} />
                                  <Route path="users" element={<UserManagement />} />
                                  <Route path="questions" element={<QuestionManagement />} />
                                  <Route path="feedback" element={<UnifiedFeedbackManagement />} />
                                  <Route path="feedback/dashboard" element={<UnifiedFeedbackManagement />} />
                                  <Route path="feedback/report" element={<UnifiedFeedbackManagement />} />
                                  <Route path="feedback/audit" element={<UnifiedFeedbackManagement />} />
                                  <Route path="bulk-generator" element={<BulkQuestionGenerator />} />
                                  <Route path="assessments" element={<AssessmentManagement />} />
                                  <Route path="public-assessments" element={<PublicAssessmentManagement />} />
                                  <Route path="templates" element={<TemplateManagement />} />
                                  <Route path="subscriptions" element={<SubscriptionOverview />} />
                                  <Route path="notifications" element={<NotificationManagement />} />
                                  <Route path="analytics" element={<AdminAnalytics />} />
                                  <Route path="seo" element={<SEOManagement />} />
                                  <Route path="settings" element={<SystemSettings />} />
                                  <Route path="ai-providers" element={<AIProviderSettings />} />
                                  <Route path="plans" element={<PlanManagement />} />
                                  <Route path="schools" element={<SchoolManagement />} />
                                </Route>
                              </Routes>
                            </AdminProtectedRoute>
                          </ProtectedRoute>
                        } />

                        {/* Catch-all route for 404 */}
                        <Route path="*" element={<NotFoundPage />} />
                      </Routes>
                    </div>
                  </QuestionSelectionProvider>
                </ThemeProvider>
              </SubscriptionProvider>
            </AuthProvider>
          </QueryClientProvider>
        </Router>
      </HelmetProvider>
    </ErrorBoundary>
  )
}

export default App