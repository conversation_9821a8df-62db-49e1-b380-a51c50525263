import React, { useState } from 'react'
import { 
  Users, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Shield, 
  ShieldOff,
  Mail,
  Calendar,
  Crown,
  Loader,
  AlertTriangle,
  RefreshCw,
  Plus,
  X
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Save } from 'lucide-react'

type Profile = Database['public']['Tables']['profiles']['Row']
type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

interface UserEditModalProps {
  user: Profile
  onClose: () => void
  onSave: (userId: string, updates: ProfileUpdate) => Promise<void>
  isLoading: boolean
}

const UserEditModal: React.FC<UserEditModalProps> = ({ user, onClose, onSave, isLoading }) => {
  const [editedUser, setEditedUser] = useState<ProfileUpdate>({
    nome: user.nome,
    email: user.email,
    escola: user.escola,
    is_admin: user.is_admin,
    plano: user.plano,
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement
    setEditedUser(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSave(user.id, editedUser)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Editar Usuário</h2>
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="nome" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Nome</label>
            <input
              type="text"
              id="nome"
              name="nome"
              value={editedUser.nome || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={editedUser.email || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="escola" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Escola</label>
            <input
              type="text"
              id="escola"
              name="escola"
              value={editedUser.escola || ''}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label htmlFor="plano" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Plano</label>
            <select
              id="plano"
              name="plano"
              value={editedUser.plano || 'gratuito'}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            >
              <option value="gratuito">Gratuito</option>
              <option value="premium">Premium</option>
              <option value="escolar">Escolar</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_admin"
              name="is_admin"
              checked={editedUser.is_admin || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_admin" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">É Administrador</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Salvando...' : 'Salvar Alterações'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

interface UserCreateModalProps {
  onClose: () => void;
  onSave: (userData: { email: string; password?: string; nome: string; escola?: string; plano: string; is_admin: boolean }) => Promise<void>;
  isLoading: boolean;
}

const UserCreateModal: React.FC<UserCreateModalProps> = ({ onClose, onSave, isLoading }) => {
  const [newUserData, setNewUserData] = useState({
    email: '',
    password: '',
    nome: '',
    escola: '',
    plano: 'gratuito',
    is_admin: false,
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement
    setNewUserData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newUserData.email || !newUserData.nome || !newUserData.password) {
      toast.error('Por favor, preencha todos os campos obrigatórios (Email, Nome e Senha).')
      return
    }
    await onSave(newUserData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fade-in">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative max-h-[90vh] overflow-y-auto"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Novo Usuário</h2>
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="nome" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Nome</label>
            <input
              type="text"
              id="nome"
              name="nome"
              value={newUserData.nome}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={newUserData.email}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Senha</label>
            <input
              type="password"
              id="password"
              name="password"
              value={newUserData.password}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="escola" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Escola (Opcional)</label>
            <input
              type="text"
              id="escola"
              name="escola"
              value={newUserData.escola}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label htmlFor="plano" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Plano</label>
            <select
              id="plano"
              name="plano"
              value={newUserData.plano}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            >
              <option value="gratuito">Gratuito</option>
              <option value="premium">Premium</option>
              <option value="escolar">Escolar</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_admin"
              name="is_admin"
              checked={newUserData.is_admin}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_admin" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">É Administrador</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Criando...' : 'Criar Usuário'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

const UserManagement: React.FC = () => {
  const queryClient = useQueryClient()

  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [planFilter, setPlanFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [usersPerPage] = useState(10)

  const [selectedUser, setSelectedUser] = useState<Profile | null>(null)
  const [showUserModal, setShowUserModal] = useState(false)
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)
  const [showCreateUserModal, setShowCreateUserModal] = useState(false)

  const { data: users, isLoading, error, refetch, isRefetching } = useQuery<Profile[], Error>({
    queryKey: ['users', searchTerm, roleFilter, planFilter, currentPage],
    queryFn: async () => {
      let query = supabase.from('profiles').select('*')

      if (searchTerm) {
        query = query.or(`nome.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      }

      if (roleFilter === 'admin') {
        query = query.eq('is_admin', true)
      } else if (roleFilter === 'user') {
        query = query.eq('is_admin', false)
      }

      if (planFilter === 'premium') {
        query = query.eq('plano', 'premium')
      } else if (planFilter === 'free') {
        query = query.eq('plano', 'gratuito')
      } else if (planFilter === 'escolar') {
        query = query.eq('plano', 'escolar')
      }

      const from = (currentPage - 1) * usersPerPage
      const to = from + usersPerPage - 1
      query = query.order('created_at', { ascending: false }).range(from, to)

      const { data, error: fetchError } = await query

      if (fetchError) {
        throw fetchError
      }
      return data || []
    },
    staleTime: 1000 * 60 * 2, // 2 minutos
    gcTime: 1000 * 60 * 5, // 5 minutos
    placeholderData: (previousData) => previousData,
    onError: (err) => {
      console.error('Error fetching users:', err)
      toast.error('Erro ao carregar usuários.')
    }
  })

  const { data: totalUsersCount } = useQuery<number, Error>({
    queryKey: ['totalUsersCount', searchTerm, roleFilter, planFilter],
    queryFn: async () => {
      let query = supabase.from('profiles').select('*', { count: 'exact', head: true })

      if (searchTerm) {
        query = query.or(`nome.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      }

      if (roleFilter === 'admin') {
        query = query.eq('is_admin', true)
      } else if (roleFilter === 'user') {
        query = query.eq('is_admin', false)
      }

      if (planFilter === 'premium') {
        query = query.eq('plano', 'premium')
      } else if (planFilter === 'free') {
        query = query.eq('plano', 'gratuito')
      } else if (planFilter === 'escolar') {
        query = query.eq('plano', 'escolar')
      }

      const { count, error: countError } = await query
      if (countError) throw countError
      return count || 0
    },
    staleTime: 1000 * 60 * 5,
    onError: (err) => {
      console.error('Error fetching total user count:', err)
    }
  })

  const pageCount = Math.ceil((totalUsersCount || 0) / usersPerPage)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    setOpenDropdownId(null)
  }

  const handleNextPage = () => {
    if (currentPage < pageCount) {
      setCurrentPage(prev => prev + 1)
      setOpenDropdownId(null)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
      setOpenDropdownId(null)
    }
  }

  const createUserMutation = useMutation({
    mutationFn: async (userData: { email: string; password?: string; nome: string; escola?: string; plano: string; is_admin: boolean }) => {
      // Verificar se o email já existe
      const { data: existingUser, error: checkError } = await supabase
        .from('profiles')
        .select('email')
        .eq('email', userData.email.toLowerCase())
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 = No rows found, que é o que queremos
        throw new Error(`Erro ao verificar email: ${checkError.message}`)
      }

      if (existingUser) {
        throw new Error('Este email já está sendo usado por outro usuário')
      }

      let authUserId: string | undefined;
      if (userData.password) {
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true,
        });

        if (authError) {
          throw authError;
        }
        authUserId = authData.user?.id;
      }

      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authUserId,
          nome: userData.nome,
          email: userData.email,
          escola: userData.escola || null,
          plano: userData.plano,
          is_admin: userData.is_admin,
          created_at: new Date().toISOString(),
          last_modified_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (profileError) {
        if (authUserId) {
          await supabase.auth.admin.deleteUser(authUserId);
        }
        throw profileError;
      }
      return profileData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
      toast.success('Usuário criado com sucesso!')
      setShowCreateUserModal(false)
    },
    onError: (error: any) => {
      console.error('Error creating user:', error)
      toast.error(`Erro ao criar usuário: ${error.message || 'Erro desconhecido'}`)
    },
  })

  const handleCreateUser = async (userData: { email: string; password?: string; nome: string; escola?: string; plano: string; is_admin: boolean }) => {
    await createUserMutation.mutateAsync(userData)
  }

  const toggleAdminStatusMutation = useMutation({
    mutationFn: async ({ userId, newStatus }: { userId: string; newStatus: boolean }) => {
      const { error } = await supabase
        .from('profiles')
        .update({ is_admin: newStatus })
        .eq('id', userId)
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
      toast.success('Status de administrador atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error toggling admin status:', error)
      toast.error('Erro ao atualizar status de administrador.')
    },
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase.from('profiles').delete().eq('id', userId)
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
      toast.success('Usuário excluído com sucesso!')
      setOpenDropdownId(null)
    },
    onError: (error) => {
      console.error('Error deleting user:', error)
      toast.error('Erro ao excluir usuário.')
    },
  })

  const editUserMutation = useMutation({
    mutationFn: async ({ userId, updates }: { userId: string; updates: ProfileUpdate }) => {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
      toast.success('Usuário atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Error updating user:', error)
      toast.error('Erro ao atualizar usuário.')
    },
  })

  const handleEditUser = async (userId: string, updates: ProfileUpdate) => {
    try {
      const { data, error: updateError } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (updateError) throw updateError
      toast.success('Usuário atualizado com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
      handleCloseEditModal()
    } catch (error: any) {
      console.error('Error updating user:', error)
      toast.error(`Erro ao atualizar usuário: ${error.message || 'Erro desconhecido'}`)
    }
  }

  const handleOpenEditModal = (user: Profile) => {
    setSelectedUser(user)
    setShowUserModal(true)
    setOpenDropdownId(null)
  }

  const handleCloseEditModal = () => {
    setSelectedUser(null)
    setShowUserModal(false)
  }

  const handleOpenCreateUserModal = () => {
    setShowCreateUserModal(true)
  }

  const handleCloseCreateUserModal = () => {
    setShowCreateUserModal(false)
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando usuários...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-600 dark:text-red-400">
            <AlertTriangle className="w-8 h-8 mx-auto mb-4" />
            <p>Erro ao carregar usuários: {error.message}</p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 flex flex-col min-h-screen">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Gerenciamento de Usuários</h1>
          <p className="text-gray-600 dark:text-gray-400">{totalUsersCount} usuários encontrados</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleOpenCreateUserModal}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5 mr-2" /> Criar Novo Usuário
          </button>
          <button
            onClick={() => refetch()}
            disabled={isRefetching}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRefetching ? <Loader className="w-5 h-5 mr-2 animate-spin" /> : <RefreshCw className="w-5 h-5 mr-2" />}
            Atualizar Lista
          </button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar usuários por nome ou email..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        
        <select
          value={roleFilter}
          onChange={(e) => {
            setRoleFilter(e.target.value)
            setCurrentPage(1)
          }}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">Todos os Papéis</option>
          <option value="admin">Administradores</option>
          <option value="user">Usuários Comuns</option>
        </select>

        <select
          value={planFilter}
          onChange={(e) => {
            setPlanFilter(e.target.value)
            setCurrentPage(1)
          }}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">Todos os Planos</option>
          <option value="premium">Premium</option>
          <option value="escolar">Escolar</option>
          <option value="gratuito">Gratuito</option>
        </select>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 flex-1 overflow-y-auto">
        <div>
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
              <tr>
                <th className="text-left py-4 px-6 font-medium text-gray-900 dark:text-white">Usuário</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900 dark:text-white">Plano</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900 dark:text-white">Status</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900 dark:text-white">Cadastro</th>
                <th className="text-right py-4 px-6 font-medium text-gray-900 dark:text-white">Ações</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {(users || []).map((user, index) => (
                <motion.tr
                  key={user.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                >
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                          {user.nome ? user.nome.charAt(0).toUpperCase() : ''}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{user.nome}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{user.email}</p>
                        {user.escola && (
                          <p className="text-xs text-gray-500 dark:text-gray-500">{user.escola}</p>
                        )}
                      </div>
                    </div>
                  </td>
                  
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        user.plano === 'premium' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300' :
                        user.plano === 'escolar' ? 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300' :
                        'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                      }`}>
                        {user.plano === 'premium' && <Crown className="w-4 h-4 mr-1" />}
                        {user.plano === 'escolar' && <Users className="w-4 h-4 mr-1" />}
                        {user.plano ? user.plano.charAt(0).toUpperCase() + user.plano.slice(1) : 'Grátis'}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      user.is_admin ? 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300' :
                      'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300'
                    }`}>
                      {user.is_admin ? 'Admin' : 'Usuário'}
                    </span>
                  </td>
                  <td className="py-4 px-6 text-gray-600 dark:text-gray-400">
                    {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="py-4 px-6 text-right">
                    <div className="relative inline-block text-left">
                      <button
                        type="button"
                        className="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        onClick={() => setOpenDropdownId(openDropdownId === user.id ? null : user.id)}
                      >
                        <MoreVertical className="w-5 h-5" />
                      </button>
                      {openDropdownId === user.id && (
                        <div
                          className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                          role="menu"
                          aria-orientation="vertical"
                        >
                          <div className="py-1" role="none">
                            <button
                              onClick={() => handleOpenEditModal(user)}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                              role="menuitem"
                            >
                              <Edit className="w-4 h-4 mr-2" /> Editar
                            </button>
                            <button
                              onClick={() => toggleAdminStatusMutation.mutate({ userId: user.id, newStatus: !user.is_admin })}
                              className={`flex items-center w-full px-4 py-2 text-sm ${
                                user.is_admin ? 'text-red-600 hover:bg-red-100 dark:hover:bg-red-900' : 'text-green-600 hover:bg-green-100 dark:hover:bg-green-900'
                              }`}
                              role="menuitem"
                            >
                              {user.is_admin ? <ShieldOff className="w-4 h-4 mr-2" /> : <Shield className="w-4 h-4 mr-2" />}
                              {user.is_admin ? 'Remover Admin' : 'Tornar Admin'}
                            </button>
                            <button
                              onClick={() => deleteUserMutation.mutate(user.id)}
                              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-100 dark:hover:bg-red-900"
                              role="menuitem"
                            >
                              <Trash2 className="w-4 h-4 mr-2" /> Excluir
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </motion.tr>
              ))}
              {users && users.length === 0 && (
                <tr>
                  <td colSpan={5} className="py-8 text-center text-gray-500 dark:text-gray-400">
                    Nenhum usuário encontrado.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex justify-between items-center mt-6">
        <button
          onClick={handlePrevPage}
          disabled={currentPage === 1 || isLoading}
          className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Anterior
        </button>
        <span className="text-gray-700 dark:text-gray-300">
          Página {currentPage} de {pageCount}
        </span>
        <button
          onClick={handleNextPage}
          disabled={currentPage === pageCount || isLoading}
          className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Próxima
        </button>
      </div>

      {showUserModal && selectedUser && (
        <UserEditModal
          user={selectedUser}
          onClose={handleCloseEditModal}
          onSave={handleEditUser}
          isLoading={editUserMutation.isPending}
        />
      )}

      {showCreateUserModal && (
        <UserCreateModal
          onClose={handleCloseCreateUserModal}
          onSave={handleCreateUser}
          isLoading={createUserMutation.isPending}
        />
      )}
    </div>
  )
}

export default UserManagement