import React, { forwardRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react'
import { FieldError } from 'react-hook-form'

interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  error?: FieldError
  success?: boolean
  helperText?: string
  icon?: React.ComponentType<any>
  showPasswordToggle?: boolean
  onTogglePassword?: () => void
  showPassword?: boolean
  isTextarea?: boolean
  textareaProps?: React.TextareaHTMLAttributes<HTMLTextAreaElement>
}

const FormField = forwardRef<HTMLInputElement | HTMLTextAreaElement, FormFieldProps>(
  ({
    label,
    error,
    success,
    helperText,
    icon: Icon,
    showPasswordToggle,
    onTogglePassword,
    showPassword,
    isTextarea = false,
    textareaProps,
    className = '',
    ...props
  }, ref) => {
    const hasError = !!error
    const hasSuccess = success && !hasError

    const inputClasses = `
      w-full px-4 py-3 border rounded-lg transition-all duration-200
      ${Icon ? 'pl-12' : ''}
      ${showPasswordToggle ? 'pr-12' : ''}
      ${hasError 
        ? 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20 focus:border-red-500 focus:ring-red-500' 
        : hasSuccess
        ? 'border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20 focus:border-green-500 focus:ring-green-500'
        : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:border-blue-500 focus:ring-blue-500'
      }
      text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400
      focus:outline-none focus:ring-2 focus:ring-opacity-50
      disabled:opacity-50 disabled:cursor-not-allowed
      ${className}
    `

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
          {props.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        <div className="relative">
          {Icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon className={`h-5 w-5 ${
                hasError 
                  ? 'text-red-400' 
                  : hasSuccess 
                  ? 'text-green-400' 
                  : 'text-gray-400'
              }`} />
            </div>
          )}
          
          {isTextarea ? (
            <textarea
              ref={ref as React.Ref<HTMLTextAreaElement>}
              className={inputClasses}
              {...textareaProps}
              {...(props as any)}
            />
          ) : (
            <input
              ref={ref as React.Ref<HTMLInputElement>}
              className={inputClasses}
              {...props}
            />
          )}
          
          {showPasswordToggle && (
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={onTogglePassword}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          )}
          
          {hasSuccess && !showPasswordToggle && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <CheckCircle className="h-5 w-5 text-green-400" />
            </div>
          )}
        </div>
        
        <AnimatePresence mode="wait">
          {(error || helperText) && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="flex items-start space-x-2"
            >
              {error && (
                <>
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {error.message}
                  </p>
                </>
              )}
              {!error && helperText && (
                <p className="text-sm text-gray-500 dark:text-gray-400 ml-6">
                  {helperText}
                </p>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }
)

FormField.displayName = 'FormField'

export default FormField
