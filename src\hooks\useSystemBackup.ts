import { useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { toast } from 'react-toastify'

export interface SystemBackup {
  id: string
  name: string
  description?: string
  settings: Record<string, any>
  created_at: string
  created_by: string
  version: string
}

export const useSystemBackup = () => {
  const { user } = useAuth()

  // Criar backup das configurações atuais
  const createBackup = useCallback(async (name: string, description?: string): Promise<boolean> => {
    if (!user) {
      toast.error('Usuário não autenticado')
      return false
    }

    try {
      // Buscar todas as configurações atuais
      const { data: currentSettings, error: fetchError } = await supabase
        .from('system_settings')
        .select('*')

      if (fetchError) {
        throw fetchError
      }

      // Criar objeto de backup
      const backupData: Omit<SystemBackup, 'id'> = {
        name,
        description,
        settings: currentSettings?.reduce((acc, setting) => {
          acc[setting.key] = {
            value: setting.value,
            type: setting.type,
            description: setting.description,
            category: setting.category
          }
          return acc
        }, {} as Record<string, any>) || {},
        created_at: new Date().toISOString(),
        created_by: user.id,
        version: '1.0.0'
      }

      // Salvar backup
      const { error: saveError } = await supabase
        .from('system_backups')
        .insert([backupData])

      if (saveError) {
        throw saveError
      }

      toast.success('Backup criado com sucesso!')
      return true
    } catch (error: any) {
      console.error('Error creating backup:', error)
      toast.error(`Erro ao criar backup: ${error.message}`)
      return false
    }
  }, [user])

  // Restaurar configurações de um backup
  const restoreBackup = useCallback(async (backupId: string): Promise<boolean> => {
    if (!user) {
      toast.error('Usuário não autenticado')
      return false
    }

    try {
      // Buscar dados do backup
      const { data: backup, error: fetchError } = await supabase
        .from('system_backups')
        .select('*')
        .eq('id', backupId)
        .single()

      if (fetchError) {
        throw fetchError
      }

      if (!backup) {
        throw new Error('Backup não encontrado')
      }

      // Restaurar cada configuração
      const settingsToRestore = Object.entries(backup.settings).map(([key, config]: [string, any]) => ({
        key,
        value: config.value,
        type: config.type,
        description: config.description,
        category: config.category,
        updated_at: new Date().toISOString(),
        updated_by: user.id
      }))

      // Usar upsert para atualizar ou inserir configurações
      const { error: restoreError } = await supabase
        .from('system_settings')
        .upsert(settingsToRestore, { onConflict: 'key' })

      if (restoreError) {
        throw restoreError
      }

      toast.success('Configurações restauradas com sucesso!')
      return true
    } catch (error: any) {
      console.error('Error restoring backup:', error)
      toast.error(`Erro ao restaurar backup: ${error.message}`)
      return false
    }
  }, [user])

  // Listar backups disponíveis
  const listBackups = useCallback(async (): Promise<SystemBackup[]> => {
    try {
      const { data, error } = await supabase
        .from('system_backups')
        .select(`
          *,
          profiles:created_by (
            nome,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      return data || []
    } catch (error: any) {
      console.error('Error listing backups:', error)
      toast.error(`Erro ao listar backups: ${error.message}`)
      return []
    }
  }, [])

  // Deletar backup
  const deleteBackup = useCallback(async (backupId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('system_backups')
        .delete()
        .eq('id', backupId)

      if (error) {
        throw error
      }

      toast.success('Backup deletado com sucesso!')
      return true
    } catch (error: any) {
      console.error('Error deleting backup:', error)
      toast.error(`Erro ao deletar backup: ${error.message}`)
      return false
    }
  }, [])

  // Exportar backup como JSON
  const exportBackup = useCallback(async (backupId: string): Promise<void> => {
    try {
      const { data: backup, error } = await supabase
        .from('system_backups')
        .select('*')
        .eq('id', backupId)
        .single()

      if (error) {
        throw error
      }

      if (!backup) {
        throw new Error('Backup não encontrado')
      }

      // Criar arquivo JSON para download
      const dataStr = JSON.stringify(backup, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `system-backup-${backup.name}-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast.success('Backup exportado com sucesso!')
    } catch (error: any) {
      console.error('Error exporting backup:', error)
      toast.error(`Erro ao exportar backup: ${error.message}`)
    }
  }, [])

  return {
    createBackup,
    restoreBackup,
    listBackups,
    deleteBackup,
    exportBackup
  }
}
