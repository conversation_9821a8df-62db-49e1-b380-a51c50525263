import React from 'react'
import { FileText } from 'lucide-react'
import { AssessmentConfig } from '../../../types/assessment'

interface FooterSettingsProps {
  config: AssessmentConfig
  onConfigChange: (config: AssessmentConfig) => void
  isPremium: boolean
  isEscolar: boolean
}

const FooterSettings: React.FC<FooterSettingsProps> = ({
  config,
  onConfigChange,
  isPremium,
  isEscolar
}) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
          <FileText className="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Configurações do Rodapé
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Configure as informações que aparecerão no rodapé do documento
          </p>
        </div>
      </div>

      <div className="space-y-4">
        {/* Opção de rodapé para usuários pagos */}
        {(isPremium || isEscolar) ? (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <label className="flex items-start space-x-3">
              <input
                type="checkbox"
                checked={config.showFooter}
                onChange={e => onConfigChange({ ...config, showFooter: e.target.checked })}
                className="mt-1 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
              />
              <div>
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Exibir rodapé "Gerado pela plataforma Atividade Pronta"
                </span>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  Como usuário premium, você pode escolher se deseja exibir ou ocultar o rodapé da plataforma.
                </p>
              </div>
            </label>
          </div>
        ) : (
          <div className="p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                checked={true}
                disabled={true}
                className="mt-1 text-gray-400 cursor-not-allowed"
              />
              <div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Exibir rodapé "Gerado pela plataforma Atividade Pronta"
                </span>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  Usuários do plano gratuito sempre terão o rodapé da plataforma exibido. 
                  Faça upgrade para o plano Premium para ter controle total sobre o rodapé.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Configurações adicionais do rodapé */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Texto Personalizado do Rodapé
            </label>
            <textarea
              value={config.footerConfig?.customText || ''}
              onChange={(e) => onConfigChange({
                ...config,
                footerConfig: {
                  ...config.footerConfig,
                  customText: e.target.value
                }
              })}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Adicione um texto personalizado ao rodapé (opcional)"
            />
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
              Este texto aparecerá no rodapé junto com outras informações
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.footerConfig?.showPageNumbers || false}
                onChange={(e) => onConfigChange({
                  ...config,
                  footerConfig: {
                    ...config.footerConfig,
                    showPageNumbers: e.target.checked
                  }
                })}
                className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
              />
              <div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Numeração de Páginas
                </span>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Exibe "Página X de Y" no rodapé
                </p>
              </div>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.footerConfig?.showDate || false}
                onChange={(e) => onConfigChange({
                  ...config,
                  footerConfig: {
                    ...config.footerConfig,
                    showDate: e.target.checked
                  }
                })}
                className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
              />
              <div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Data de Geração
                </span>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Mostra quando o PDF foi gerado
                </p>
              </div>
            </label>
          </div>
        </div>

        {/* Preview do rodapé */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Preview do Rodapé
          </h4>
          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-xs text-gray-600 dark:text-gray-400 text-center space-y-1">
              {config.footerConfig?.customText && (
                <div>{config.footerConfig.customText}</div>
              )}
              
              <div className="flex justify-between items-center">
                <span>
                  {config.footerConfig?.showDate && "Gerado em: " + new Date().toLocaleDateString('pt-BR')}
                </span>
                <span>
                  {config.footerConfig?.showPageNumbers && "Página 1 de 3"}
                </span>
              </div>
              
              {(config.showFooter || (!isPremium && !isEscolar)) && (
                <div className="border-t border-gray-300 dark:border-gray-600 pt-1 mt-2">
                  Gerado pela plataforma Atividade Pronta
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FooterSettings
