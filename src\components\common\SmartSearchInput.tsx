import React, { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, Clock, TrendingUp, Filter } from 'lucide-react'

interface SearchSuggestion {
  id: string
  text: string
  type: 'recent' | 'popular' | 'suggestion'
  count?: number
}

interface SmartSearchInputProps {
  value: string
  onChange: (value: string) => void
  onSearch?: (value: string) => void
  placeholder?: string
  suggestions?: SearchSuggestion[]
  recentSearches?: string[]
  popularSearches?: string[]
  showSuggestions?: boolean
  debounceMs?: number
  minLength?: number
  maxSuggestions?: number
  className?: string
  disabled?: boolean
  autoFocus?: boolean
}

const SmartSearchInput: React.FC<SmartSearchInputProps> = ({
  value,
  onChange,
  onSearch,
  placeholder = 'Buscar...',
  suggestions = [],
  recentSearches = [],
  popularSearches = [],
  showSuggestions = true,
  debounceMs = 300,
  minLength = 2,
  maxSuggestions = 8,
  className = '',
  disabled = false,
  autoFocus = false
}) => {
  const [isFocused, setIsFocused] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [debouncedValue, setDebouncedValue] = useState(value)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Debounce the search value
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedValue(value)
      if (onSearch && value.length >= minLength) {
        onSearch(value)
      }
    }, debounceMs)

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [value, debounceMs, minLength, onSearch])

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    setShowDropdown(newValue.length >= minLength || newValue.length === 0)
  }, [onChange, minLength])

  // Handle input focus
  const handleFocus = useCallback(() => {
    setIsFocused(true)
    setShowDropdown(true)
  }, [])

  // Handle input blur
  const handleBlur = useCallback(() => {
    // Delay blur to allow clicking on suggestions
    setTimeout(() => {
      setIsFocused(false)
      setShowDropdown(false)
    }, 150)
  }, [])

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion: string) => {
    onChange(suggestion)
    if (onSearch) {
      onSearch(suggestion)
    }
    setShowDropdown(false)
    inputRef.current?.blur()
  }, [onChange, onSearch])

  // Handle clear search
  const handleClear = useCallback(() => {
    onChange('')
    if (onSearch) {
      onSearch('')
    }
    inputRef.current?.focus()
  }, [onChange, onSearch])

  // Handle key down
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (onSearch) {
        onSearch(value)
      }
      setShowDropdown(false)
      inputRef.current?.blur()
    } else if (e.key === 'Escape') {
      setShowDropdown(false)
      inputRef.current?.blur()
    }
  }, [value, onSearch])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Combine all suggestions
  const allSuggestions = React.useMemo(() => {
    const combined: SearchSuggestion[] = []

    // Add recent searches
    if (value.length === 0 && recentSearches.length > 0) {
      recentSearches.slice(0, 3).forEach(search => {
        combined.push({
          id: `recent-${search}`,
          text: search,
          type: 'recent'
        })
      })
    }

    // Add popular searches
    if (value.length === 0 && popularSearches.length > 0) {
      popularSearches.slice(0, 3).forEach(search => {
        combined.push({
          id: `popular-${search}`,
          text: search,
          type: 'popular'
        })
      })
    }

    // Add dynamic suggestions
    if (value.length >= minLength) {
      suggestions.forEach(suggestion => {
        if (suggestion.text.toLowerCase().includes(value.toLowerCase())) {
          combined.push(suggestion)
        }
      })
    }

    return combined.slice(0, maxSuggestions)
  }, [value, recentSearches, popularSearches, suggestions, minLength, maxSuggestions])

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent':
        return <Clock className="w-4 h-4 text-gray-400" />
      case 'popular':
        return <TrendingUp className="w-4 h-4 text-gray-400" />
      default:
        return <Search className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          autoFocus={autoFocus}
          className={`
            w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 
            rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent 
            dark:bg-gray-800 dark:text-white transition-all duration-200
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${isFocused ? 'ring-2 ring-blue-500 border-transparent' : ''}
          `}
        />
        
        {value && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        )}
      </div>

      {/* Suggestions dropdown */}
      <AnimatePresence>
        {showSuggestions && showDropdown && allSuggestions.length > 0 && (
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-64 overflow-y-auto"
          >
            {value.length === 0 && (recentSearches.length > 0 || popularSearches.length > 0) && (
              <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <Filter className="w-3 h-3" />
                  <span>Sugestões</span>
                </div>
              </div>
            )}
            
            {allSuggestions.map((suggestion) => (
              <button
                key={suggestion.id}
                type="button"
                onClick={() => handleSuggestionClick(suggestion.text)}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 flex items-center space-x-3"
              >
                {getSuggestionIcon(suggestion.type)}
                <span className="flex-1 text-gray-900 dark:text-white">
                  {suggestion.text}
                </span>
                {suggestion.count && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {suggestion.count}
                  </span>
                )}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SmartSearchInput
