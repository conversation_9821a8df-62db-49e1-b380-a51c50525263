import React, { useState } from 'react'
import { 
  Database, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle,
  User,
  Calendar,
  FileText,
  EyeOff,
  Loader,
  AlertTriangle,
  RefreshCw,
  Plus,
  Save,
  MoreVertical
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { Database as DatabaseType } from '../../types/database'
import { toast } from 'react-hot-toast'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Link, useNavigate } from 'react-router-dom'
import ConfirmationModal from '../common/ConfirmationModal'

type Assessment = DatabaseType['public']['Tables']['assessments']['Row']
type AssessmentInsert = DatabaseType['public']['Tables']['assessments']['Insert']

interface AssessmentWithProfile extends Assessment {
  profiles?: {
    nome: string
    email: string
  }
}

interface AssessmentCreateModalProps {
  onClose: () => void
  onSave: (assessmentData: Omit<AssessmentInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at'>) => Promise<void>
  isLoading: boolean
}

const AssessmentCreateModal: React.FC<AssessmentCreateModalProps> = ({ onClose, onSave, isLoading }) => {
  const [newAssessmentData, setNewAssessmentData] = useState<Omit<AssessmentInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at'>>({
    titulo: '',
    descricao: '',
    disciplina: '',
    is_public: false,
    content: {},
    metadata: {},
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement
    setNewAssessmentData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newAssessmentData.titulo || !newAssessmentData.disciplina) {
      toast.error('Título e Disciplina são obrigatórios.')
      return
    }
    await onSave(newAssessmentData)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-lg relative"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Nova Avaliação</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Título</label>
            <input
              type="text"
              id="titulo"
              name="titulo"
              value={newAssessmentData.titulo}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="disciplina" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Disciplina</label>
            <input
              type="text"
              id="disciplina"
              name="disciplina"
              value={newAssessmentData.disciplina}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div>
            <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Descrição (Opcional)</label>
            <textarea
              id="descricao"
              name="descricao"
              value={newAssessmentData.descricao || ''}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_public"
              name="is_public"
              checked={newAssessmentData.is_public || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_public" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">Disponibilizar publicamente</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Criando...' : 'Criar Avaliação'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

const AssessmentManagement: React.FC = () => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [assessmentsPerPage] = useState(9)
  const [filter, setFilter] = useState<'recent' | 'popular'>('popular')
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)

  // Estados para o modal de confirmação
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [assessmentToDelete, setAssessmentToDelete] = useState<AssessmentWithProfile | null>(null)

  const { data: assessments, isLoading, error, refetch } = useQuery<AssessmentWithProfile[], Error>(
    {
      queryKey: ['assessments', searchTerm, statusFilter, currentPage],
      queryFn: async () => {
        let query = supabase
          .from('assessments')
          .select(`
            *,
            profiles (
              nome,
              email
            )
          `)

        if (searchTerm) {
          query = query.or(`titulo.ilike.%${searchTerm}%,disciplina.ilike.%${searchTerm}%,profiles.nome.ilike.%${searchTerm}%`)
        }

        if (statusFilter === 'public') {
          query = query.eq('is_public', true)
        } else if (statusFilter === 'private') {
          query = query.eq('is_public', false)
        }

        const from = (currentPage - 1) * assessmentsPerPage
        const to = from + assessmentsPerPage - 1
        query = query.order('created_at', { ascending: false }).range(from, to)

        const { data, error: fetchError } = await query

        if (fetchError) {
          throw fetchError
        }
        return data || []
      },
      staleTime: 1000 * 60 * 1,
      keepPreviousData: true,
      onError: (err) => {
        console.error('Error fetching assessments:', err)
        toast.error('Erro ao carregar avaliações.')
      },
    }
  )

  const { data: totalAssessmentsCount } = useQuery<number, Error>(
    {
      queryKey: ['totalAssessmentsCount', searchTerm, statusFilter],
      queryFn: async () => {
        let query = supabase.from('assessments').select('id', { count: 'exact', head: true })

        if (searchTerm) {
          query = query.or(`titulo.ilike.%${searchTerm}%,disciplina.ilike.%${searchTerm}%,profiles.nome.ilike.%${searchTerm}%`)
        }

        if (statusFilter === 'public') {
          query = query.eq('is_public', true)
        } else if (statusFilter === 'private') {
          query = query.eq('is_public', false)
        }

        const { count, error: countError } = await query
        if (countError) throw countError
        return count || 0
      },
      staleTime: 1000 * 60 * 1,
    }
  )

  const pageCount = Math.ceil((totalAssessmentsCount || 0) / assessmentsPerPage)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleNextPage = () => {
    if (currentPage < pageCount) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const togglePublicStatusMutation = useMutation(
    {
      mutationFn: async ({ assessmentId, currentStatus }: { assessmentId: string; currentStatus: boolean }) => {
        const { error } = await supabase
          .from('assessments')
          .update({ is_public: !currentStatus })
          .eq('id', assessmentId)

        if (error) throw error
      },
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['assessments'] })
        queryClient.invalidateQueries({ queryKey: ['totalAssessmentsCount'] })
        toast.success(
          !variables.currentStatus
            ? 'Avaliação tornada pública'
            : 'Avaliação tornada privada'
        )
      },
      onError: (mutationError) => {
        console.error('Error updating public status:', mutationError)
        toast.error('Erro ao atualizar status público')
      },
    }
  )

  const togglePublicStatus = (assessmentId: string, currentStatus: boolean) => {
    togglePublicStatusMutation.mutate({ assessmentId, currentStatus })
  }

  const deleteAssessmentMutation = useMutation(
    {
      mutationFn: async (assessmentId: string) => {
        const { error } = await supabase
          .from('assessments')
          .delete()
          .eq('id', assessmentId)

        if (error) throw error
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['assessments'] })
        queryClient.invalidateQueries({ queryKey: ['totalAssessmentsCount'] })
        toast.success('Avaliação excluída com sucesso')
        setShowDeleteModal(false)
        setAssessmentToDelete(null)
      },
      onError: (mutationError) => {
        console.error('Error deleting assessment:', mutationError)
        toast.error('Erro ao excluir avaliação')
      },
    }
  )

  const handleDeleteClick = (assessment: AssessmentWithProfile) => {
    setAssessmentToDelete(assessment)
    setShowDeleteModal(true)
    setOpenDropdownId(null)
  }

  const handleDeleteConfirm = () => {
    if (assessmentToDelete) {
      deleteAssessmentMutation.mutate(assessmentToDelete.id)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setAssessmentToDelete(null)
  }

  const createAssessmentMutation = useMutation({
    mutationFn: async (assessmentData: Omit<AssessmentInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at'>) => {
      const { data, error } = await supabase
        .from('assessments')
        .insert({
          ...assessmentData,
          user_id: (await supabase.auth.getUser()).data.user?.id || 'admin-system',
          created_at: new Date().toISOString(),
          last_modified_at: new Date().toISOString(),
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      queryClient.invalidateQueries({ queryKey: ['totalAssessmentsCount'] })
      toast.success('Avaliação criada com sucesso!')
    },
    onError: (error) => {
      console.error('Error creating assessment:', error)
      toast.error(`Erro ao criar avaliação: ${error.message}`)
    },
  })

  const handleCreateAssessment = async (assessmentData: Omit<AssessmentInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at'>) => {
    await createAssessmentMutation.mutateAsync(assessmentData)
  }

  const handleOpenCreateAssessmentModal = () => {
    navigate('/app/editor')
  }

  const { data: highlightedAssessments, isLoading: highlightedAssessmentsLoading } = useQuery({
    queryKey: ['highlightedAssessments', filter],
    queryFn: async () => {
      let query = supabase.from('assessments').select('id, titulo, created_at, autor_id, view_count, download_count')
      if (filter === 'popular') {
        query = query.order('view_count', { ascending: false })
      } else {
        query = query.order('created_at', { ascending: false })
      }
      const { data } = await query.limit(10)
      return data || []
    },
  })

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando avaliações...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-600 dark:text-red-400">
            <AlertTriangle className="w-8 h-8 mx-auto mb-4" />
            <p>Erro ao carregar avaliações: {error.message}</p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Gerenciamento de Avaliações</h1>
          <p className="text-gray-600 dark:text-gray-400">{totalAssessmentsCount} avaliações encontradas</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleOpenCreateAssessmentModal}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5 mr-2" /> Criar Nova Avaliação
          </button>
          <button
            onClick={() => refetch()}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <RefreshCw className="w-5 h-5 mr-2" /> Atualizar
          </button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar avaliações por título, disciplina ou autor..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => {
            setStatusFilter(e.target.value)
            setCurrentPage(1)
          }}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">Todos os Status</option>
          <option value="public">Públicas</option>
          <option value="private">Privadas</option>
        </select>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {assessments && assessments.length > 0 ? (
          assessments.map((assessment) => (
            <motion.div
              key={assessment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 flex flex-col"
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white line-clamp-2">{assessment.titulo}</h3>
                <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                  {assessment.is_public ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                      Pública
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300">
                      Privada
                    </span>
                  )}
                  <div className="relative inline-block text-left">
                    <button
                      onClick={() => setOpenDropdownId(openDropdownId === assessment.id ? null : assessment.id)}
                      className="p-1 rounded-full text-gray-500 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                      aria-expanded={openDropdownId === assessment.id ? 'true' : 'false'}
                      aria-haspopup="true"
                    >
                      <MoreVertical className="w-5 h-5" />
                    </button>
                    {openDropdownId === assessment.id && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="menu-button"
                        tabIndex={-1}
                      >
                        <div className="py-1" role="none">
                          <Link
                            to={`/app/editor/${assessment.id}`}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            <Edit className="mr-3 h-4 w-4 text-blue-500" />
                            Editar Avaliação
                          </Link>
                          <button
                            onClick={() => {
                              togglePublicStatus(assessment.id, assessment.is_public || false)
                              setOpenDropdownId(null)
                            }}
                            disabled={togglePublicStatusMutation.isLoading}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            {togglePublicStatusMutation.isLoading ? (
                              <Loader className="mr-3 h-4 w-4 animate-spin" />
                            ) : assessment.is_public ? (
                              <EyeOff className="mr-3 h-4 w-4 text-orange-500" />
                            ) : (
                              <Eye className="mr-3 h-4 w-4 text-green-500" />
                            )}
                            {assessment.is_public ? 'Tornar Privada' : 'Tornar Pública'}
                          </button>
                          <button
                            onClick={() => handleDeleteClick(assessment)}
                            disabled={deleteAssessmentMutation.isLoading}
                            className="text-gray-700 dark:text-gray-300 flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            role="menuitem"
                            tabIndex={-1}
                          >
                            {deleteAssessmentMutation.isLoading ? (
                              <Loader className="mr-3 h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="mr-3 h-4 w-4 text-red-500" />
                            )}
                            Excluir Avaliação
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 flex-1 line-clamp-3">
                {assessment.descricao || 'Nenhuma descrição disponível.'}
              </p>
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-auto">
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>{assessment.profiles?.nome || 'Desconhecido'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(assessment.created_at || '').toLocaleDateString('pt-BR')}</span>
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500 dark:text-gray-400">
            Nenhuma avaliação encontrada.
          </div>
        )}
      </div>

      {pageCount > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-4">
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Anterior
          </button>
          {Array.from({ length: pageCount }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 rounded-md ${
                currentPage === page
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={handleNextPage}
            disabled={currentPage === pageCount}
            className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Próxima
          </button>
        </div>
      )}

      <div className="mt-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Avaliações em Destaque</h2>
        <div className="flex space-x-2 mb-4">
          <button onClick={() => setFilter('popular')} className={`px-3 py-1 rounded ${filter==='popular'?'bg-blue-600 text-white':'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Mais usadas</button>
          <button onClick={() => setFilter('recent')} className={`px-3 py-1 rounded ${filter==='recent'?'bg-blue-600 text-white':'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Mais recentes</button>
        </div>
        {highlightedAssessmentsLoading ? (
          <div className="flex justify-center items-center h-40"><Loader className="w-8 h-8 animate-spin" /></div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {highlightedAssessments.map((a: any) => (
              <div key={a.id} className="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{a.titulo}</h2>
                <p className="text-xs text-gray-500 mb-1">Autor: {a.autor || 'Desconhecido'}</p>
                <p className="text-xs text-gray-400 mb-1">Criada em: {new Date(a.created_at).toLocaleDateString('pt-BR')}</p>
                <p className="text-xs text-gray-400 mb-2">Respostas: {a.respostas || 0}</p>
                <button className="mt-auto flex items-center space-x-2 text-blue-600 hover:underline"><Eye className="w-4 h-4" /><span>Ver detalhes</span></button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal de Confirmação de Exclusão */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Excluir Avaliação"
        message={`Tem certeza que deseja excluir a avaliação "${assessmentToDelete?.titulo}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        type="danger"
        isLoading={deleteAssessmentMutation.isLoading}
      />
    </div>
  )
}

export default AssessmentManagement