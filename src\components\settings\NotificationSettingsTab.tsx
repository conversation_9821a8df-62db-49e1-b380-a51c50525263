import React from 'react'
import { <PERSON>, <PERSON> } from 'lucide-react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const notificationSchema = z.object({
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  newQuestionNotifications: z.boolean(),
  systemUpdatesNotifications: z.boolean(),
})

type NotificationFormData = z.infer<typeof notificationSchema>

interface NotificationSettingsTabProps {
  profile: any
  savingNotifications: boolean
  onSaveNotifications: (data: NotificationFormData) => Promise<void>
}

const NotificationSettingsTab: React.FC<NotificationSettingsTabProps> = ({
  profile,
  savingNotifications,
  onSaveNotifications
}) => {
  const {
    register: registerNotification,
    handleSubmit: handleSubmitNotification,
    formState: { isValid: isNotificationValid },
    reset: resetNotification
  } = useForm<NotificationFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(notificationSchema),
    mode: 'onChange',
    defaultValues: {
      emailNotifications: true,
      pushNotifications: false,
      newQuestionNotifications: true,
      systemUpdatesNotifications: true
    }
  })

  // Reset form when profile changes
  React.useEffect(() => {
    if (profile) {
      const notificationSettings = profile.configuracoes?.notifications || {}
      resetNotification({
        emailNotifications: notificationSettings.email ?? true,
        pushNotifications: notificationSettings.push ?? false,
        newQuestionNotifications: notificationSettings.newQuestions ?? true,
        systemUpdatesNotifications: notificationSettings.systemUpdates ?? true
      })
    }
  }, [profile, resetNotification])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
          <Bell className="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notificações</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Configure como e quando você deseja receber notificações
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmitNotification(onSaveNotifications)} className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Notificações por Email
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receba atualizações importantes por email
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                {...registerNotification('emailNotifications')}
                type="checkbox"
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Notificações Push
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receba notificações instantâneas no navegador
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                {...registerNotification('pushNotifications')}
                type="checkbox"
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Novas Questões
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Seja notificado quando novas questões forem adicionadas
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                {...registerNotification('newQuestionNotifications')}
                type="checkbox"
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Atualizações do Sistema
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receba informações sobre atualizações e manutenções
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                {...registerNotification('systemUpdatesNotifications')}
                type="checkbox"
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            Sobre as Notificações
          </h4>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            Você pode alterar essas configurações a qualquer momento. As notificações por email são importantes 
            para manter você informado sobre atividades da sua conta e atualizações do sistema.
          </p>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!isNotificationValid || savingNotifications}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {savingNotifications ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Salvar Notificações</span>
              </>
            )}
          </button>
        </div>
      </form>
    </motion.div>
  )
}

export default NotificationSettingsTab
