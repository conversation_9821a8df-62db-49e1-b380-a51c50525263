import React from 'react'
import { Link } from 'react-router-dom'
import PublicLayout from './PublicLayout'
import PublicBreadcrumbs from './PublicBreadcrumbs'
import SEOHead from './SEOHead'
import { Home, Search, ArrowLeft, BookOpen } from 'lucide-react'

/**
 * Página de Erro 404 - Página Não Encontrada
 */
const NotFoundPage: React.FC = () => {
  const breadcrumbs = [
    { label: 'Página Não Encontrada', href: '/404', current: true }
  ]

  const seoData = {
    title: 'Página Não Encontrada | Atividade Pronta',
    description: 'A página que você está procurando não foi encontrada. Explore nossas avaliações educacionais e recursos para professores.',
    keywords: ['404', 'página não encontrada', 'atividade pronta', 'erro'],
    canonical: 'https://atvpronta.com.br/404',
    ogTitle: 'Página Não Encontrada | Atividade Pronta',
    ogDescription: 'A página que você está procurando não foi encontrada.',
    ogImage: 'https://atvpronta.com.br/og-image.jpg',
    ogUrl: 'https://atvpronta.com.br/404',
    noindex: true // Não indexar páginas de erro
  }

  return (
    <PublicLayout>
      {/* SEO Head */}
      <SEOHead
        metadata={seoData}
        breadcrumbs={breadcrumbs}
      />

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Breadcrumbs */}
        <PublicBreadcrumbs items={breadcrumbs} className="mb-6" />

        {/* Error Content */}
        <div className="text-center py-16">
          {/* Error Icon */}
          <div className="mb-8">
            <div className="mx-auto w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center">
              <Search className="w-16 h-16 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-4">
              404
            </h1>
            <h2 className="text-3xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Página Não Encontrada
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Ops! A página que você está procurando não existe ou foi movida. 
              Que tal explorar nossos recursos educacionais?
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
            >
              <Home className="w-5 h-5 mr-2" />
              Voltar ao Início
            </Link>
            
            <Link
              to="/avaliacoes"
              className="inline-flex items-center px-6 py-3 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg border border-gray-300 dark:border-gray-600 transition-colors duration-200"
            >
              <BookOpen className="w-5 h-5 mr-2" />
              Explorar Avaliações
            </Link>
          </div>

          {/* Helpful Links */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Links Úteis
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Avaliações Públicas
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Explore nossa biblioteca de avaliações gratuitas
                </p>
                <Link
                  to="/avaliacoes"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                >
                  Ver Avaliações →
                </Link>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Home className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Página Inicial
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Conheça todos os recursos da plataforma
                </p>
                <Link
                  to="/"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                >
                  Ir para Início →
                </Link>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Search className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Suporte
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Precisa de ajuda? Entre em contato conosco
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                >
                  Contatar Suporte →
                </a>
              </div>
            </div>
          </div>

          {/* Back Button */}
          <div className="mt-8">
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar à página anterior
            </button>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}

export default NotFoundPage
