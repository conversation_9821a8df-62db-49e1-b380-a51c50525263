import { AssessmentAsset } from '../hooks/useAssets'

export interface HeaderCustomization {
  customHeader?: {
    enabled: boolean
    asset?: AssessmentAsset | null
    imageUrl?: string
  }
  schoolLogo?: {
    enabled: boolean
    asset?: AssessmentAsset | null
    imageUrl?: string
  }
}

export interface AssessmentConfig {
  titulo: string
  disciplina: string
  serie: string
  headerConfig: {
    nomeEscola: string
    nomeProva: string
    serie: string
    data: string
    instrucoes: string
    // Novas opções de personalização
    customization?: HeaderCustomization
  }
  pdfOptions: {
    paperSize: 'A4' | 'Letter'
    orientation: 'portrait' | 'landscape'
    fontSize: 'small' | 'medium' | 'large'
    fontFamily: 'helvetica' | 'times' | 'courier' | 'arial' | 'verdana' | 'georgia'
    lineSpacing: 'compact' | 'normal' | 'expanded'
    includeAnswerSheet: boolean
    generateVersions: number
    watermark?: string
    addBorder?: boolean
  }
  showFooter: boolean
}

export interface Question {
  id: string
  enunciado: string
  tipo: 'multipla_escolha' | 'verdadeiro_falso' | 'dissertativa' | 'lacunas'
  opcoes?: string[]
  resposta_correta?: string | string[]
  disciplina: string
  serie: string
  dificuldade: 'facil' | 'medio' | 'dificil'
  tags?: string[]
  imagem_url?: string
  explicacao?: string
  created_at: string
  updated_at: string
  autor_id: string
  status: 'pending' | 'approved' | 'rejected'
  metadata?: Record<string, any>
}

export interface TextBlock {
  id: string
  type: 'text'
  content: string
  style?: 'normal' | 'heading' | 'subheading' | 'instruction'
  textAlign?: 'left' | 'center' | 'right' | 'justify'
}

export type AssessmentItem = Question | TextBlock
