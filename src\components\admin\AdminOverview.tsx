import React, { useState } from 'react'
import {
  Users,
  FileText,
  Database,
  CreditCard,
  Activity,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Loader,
  Bell
} from 'lucide-react'
import { motion } from 'framer-motion'
import { supabase } from '../../lib/supabase'
import { useQuery } from '@tanstack/react-query'
import { Database as DatabaseType } from '../../types/database'
import { useNotifications } from '../../hooks/useNotifications'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

type UsageStat = DatabaseType['public']['Tables']['usage_stats']['Row']

interface AdminStats {
  totalUsers: number
  totalQuestions: number
  totalAssessments: number
  activeSubscriptions: number
  recentActivityCount: number
  recentActivities: UsageStat[]
  systemHealth: 'healthy' | 'warning' | 'error'
}

const fetchAdminStats = async (): Promise<AdminStats> => {
  try {
    const { count: usersCount, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    const { count: questionsCount, error: questionsError } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true })

    const { count: assessmentsCount, error: assessmentsError } = await supabase
      .from('assessments')
      .select('*', { count: 'exact', head: true })

    const { count: subscriptionsCount, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')

    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const { data: activityData, count: activityCount, error: activityError } = await supabase
      .from('usage_stats')
      .select('*, profiles(nome, email)', { count: 'exact' })
      .gte('created_at', yesterday)
      .order('created_at', { ascending: false })
      .limit(10)

    if (usersError) throw usersError
    if (questionsError) throw questionsError
    if (assessmentsError) throw assessmentsError
    if (subscriptionsError) throw subscriptionsError
    if (activityError) throw activityError

    return {
      totalUsers: usersCount || 0,
      totalQuestions: questionsCount || 0,
      totalAssessments: assessmentsCount || 0,
      activeSubscriptions: subscriptionsCount || 0,
      recentActivityCount: activityCount || 0,
      recentActivities: activityData || [],
      systemHealth: 'healthy'
    }
  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return {
      totalUsers: 0,
      totalQuestions: 0,
      totalAssessments: 0,
      activeSubscriptions: 0,
      recentActivityCount: 0,
      recentActivities: [],
      systemHealth: 'error'
    }
  }
}

const AdminOverview: React.FC = () => {
  const { data: stats, isLoading, error, refetch, isRefetching } = useQuery<AdminStats, Error>({
    queryKey: ['adminStats'],
    queryFn: fetchAdminStats,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 1
  })

  const navigate = useNavigate()
  const { notifications, isLoading: loadingNotifications } = useNotifications()
  const [notificationFilter, setNotificationFilter] = useState<'all' | 'unread' | 'system'>('all')
  const { profile } = useAuth()
  const isAdmin = !!profile?.is_admin

  const filteredNotifications = notifications
    ? notifications.filter(n => {
        if (notificationFilter === 'unread') return !n.read
        if (notificationFilter === 'system') return n.type === 'system'
        return true
      })
    : []

  const statCards = [
    {
      label: 'Total de Usuários',
      value: stats?.totalUsers || 0,
      icon: Users,
      iconBgClass: 'bg-red-100 dark:bg-red-900/20',
      iconTextClass: 'text-red-600 dark:text-red-400'
    },
    {
      label: 'Total de Questões',
      value: stats?.totalQuestions || 0,
      icon: Database,
      iconBgClass: 'bg-blue-100 dark:bg-blue-900/20',
      iconTextClass: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Total de Avaliações',
      value: stats?.totalAssessments || 0,
      icon: FileText,
      iconBgClass: 'bg-green-100 dark:bg-green-900/20',
      iconTextClass: 'text-green-600 dark:text-green-400'
    },
    {
      label: 'Assinaturas Ativas',
      value: stats?.activeSubscriptions || 0,
      icon: CreditCard,
      iconBgClass: 'bg-purple-100 dark:bg-purple-900/20',
      iconTextClass: 'text-purple-600 dark:text-purple-400'
    }
  ]

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando estatísticas...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">
              Erro ao carregar estatísticas: {error.message}
            </p>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Por favor, tente novamente mais tarde.
            </p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">





      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Visão Geral</h1>
          <p className="text-gray-600 dark:text-gray-400">Estatísticas e métricas da plataforma</p>
        </div>
        
        <div className="flex items-center space-x-2">
          {stats?.systemHealth === 'healthy' && (
            <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm font-medium">Sistema Saudável</span>
            </div>
          )}
          {stats?.systemHealth === 'warning' && (
            <div className="flex items-center space-x-2 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle className="w-5 h-5" />
              <span className="text-sm font-medium">Atenção Necessária</span>
            </div>
          )}
          {stats?.systemHealth === 'error' && (
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span className="text-sm font-medium">Problemas Detectados</span>
            </div>
          )}
          <button
            onClick={() => refetch()}
            disabled={isRefetching}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Atualizar dados"
          >
            {isRefetching ? <Loader className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            <span>Atualizar</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={card.label}
            className="bg-white dark:bg-gray-800 rounded-xl p-5 shadow-sm border border-gray-200 dark:border-gray-700 flex items-center space-x-4 transition-transform duration-200 hover:scale-105 hover:shadow-lg group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div className={`p-3 rounded-full ${card.iconBgClass} group-hover:scale-110 transition-transform duration-200`}>
              <card.icon className={`w-7 h-7 ${card.iconTextClass} group-hover:animate-bounce`} />
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{card.label}</p>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{card.value.toLocaleString()}</h2>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Container com duas colunas para Atividade Recente e Notificações */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Atividade Recente */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 space-y-4"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-3 mb-4">
            Atividade Recente{' '}
            <span className="text-gray-500 dark:text-gray-400 text-sm">({stats?.recentActivityCount || 0} eventos nas últimas 24h)</span>
          </h2>
          {stats?.recentActivities && stats.recentActivities.length > 0 ? (
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {stats.recentActivities.map((activity, index) => (
                <li key={activity.id} className="py-3 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Activity className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.event_type === 'question_created' && `Nova questão criada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                        {activity.event_type === 'assessment_created' && `Nova avaliação criada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                        {activity.event_type === 'user_login' && `Login de usuário: ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                        {activity.event_type === 'subscription_activated' && `Nova assinatura ativada por ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                        {!activity.event_type && `Atividade desconhecida de ${activity.profiles?.nome || 'Usuário Desconhecido'}`}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.created_at).toLocaleString('pt-BR')}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400">
              Nenhuma atividade recente nas últimas 24h.
            </div>
          )}
        </motion.div>

        {/* Notificações Recentes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 space-y-4"
        >
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white border-b-0 pb-0 mb-0">
              Notificações Recentes
            </h2>
            <div className="flex items-center space-x-1">
              <button onClick={() => setNotificationFilter('all')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Todas</button>
              <button onClick={() => setNotificationFilter('unread')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'unread' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Não lidas</button>
              <button onClick={() => setNotificationFilter('system')} className={`px-2 py-1 rounded text-xs ${notificationFilter === 'system' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Sistema</button>
            </div>
          </div>
          {loadingNotifications ? (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400">Carregando notificações...</div>
          ) : filteredNotifications && filteredNotifications.length > 0 ? (
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredNotifications.slice(0, 6).map((notification) => (
                <li
                  key={notification.id}
                  className={`py-3 flex items-start space-x-3 transition-colors duration-150 rounded-lg px-2 ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500' : ''}`}
                >
                  <Bell className={`w-5 h-5 mt-1 ${!notification.read ? 'text-blue-500 animate-pulse' : 'text-gray-400'}`} />
                  <div>
                    <p className={`text-sm font-medium ${!notification.read ? 'text-blue-900 dark:text-blue-200' : 'text-gray-900 dark:text-white'}`}>{notification.title}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{new Date(notification.created_at).toLocaleString('pt-BR')}</p>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400">Nenhuma notificação recente.</div>
          )}
        </motion.div>
      </div>


    </div>
  )
}

export default AdminOverview