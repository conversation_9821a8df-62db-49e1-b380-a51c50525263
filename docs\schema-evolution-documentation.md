# Documentação de Evolução do Schema - Atividade Pronta
**Data:** 01/08/2025  
**Versão:** 1.0  

## 📋 Resumo

Este documento detalha as evoluções do schema do banco de dados desde a migração inicial (`20250614025550_jade_coast.sql`) até o estado atual em produção.

---

## 🗄️ Schema Inicial vs Atual

### **Tabela `profiles` - Evoluções Principais**

#### Schema Inicial (Migração Base):
```sql
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  nome TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  escola TEXT,
  disciplinas TEXT[] DEFAULT '{}',
  plano TEXT DEFAULT 'gratuito' CHECK (plano IN ('gratuito', 'premium', 'escolar')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);
```

#### Schema Atual (Colunas Adicionais):
```sql
-- Colunas administrativas adicionadas
is_admin BOOLEAN DEFAULT false NOT NULL,
school_id UUID REFERENCES schools(id),
is_school_admin BOOLEAN DEFAULT false NOT NULL,

-- Funcionalidades educacionais
series TEXT[] DEFAULT '{}',

-- Integração Stripe
stripe_customer_id TEXT,

-- Auditoria
last_login TIMESTAMP WITH TIME ZONE,
trial_start_date TIMESTAMP WITH TIME ZONE,
trial_end_date TIMESTAMP WITH TIME ZONE
```

**Justificativa das Evoluções:**
- `is_admin`: Sistema de administração global
- `school_id` + `is_school_admin`: Gestão de escolas e administradores escolares
- `series`: Suporte a múltiplas séries por professor
- `stripe_customer_id`: Integração com sistema de pagamentos
- Campos de trial: Sistema de período de teste

---

## 🆕 Tabelas Adicionadas (Não no Schema Inicial)

### **1. Tabela `plans` - Sistema de Planos**
```sql
CREATE TABLE plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'BRL',
  interval TEXT CHECK (interval IN ('month', 'year')),
  stripe_product_id TEXT,
  stripe_price_id TEXT,
  features JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Gestão dinâmica de planos de assinatura integrados com Stripe

### **2. Tabela `schools` - Gestão de Escolas**
```sql
CREATE TABLE schools (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  subscription_plan TEXT DEFAULT 'escolar',
  max_teachers INTEGER DEFAULT 50,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Sistema multi-tenant para escolas

### **3. Tabela `ai_generation_logs` - Auditoria de IA**
```sql
CREATE TABLE ai_generation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  provider TEXT NOT NULL,
  success BOOLEAN NOT NULL,
  questions_generated INTEGER DEFAULT 0,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10,4) DEFAULT 0,
  duration_ms INTEGER,
  cache_hit BOOLEAN DEFAULT false,
  parameters JSONB,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Monitoramento e auditoria do sistema de geração de questões por IA

### **4. Tabela `ai_provider_settings` - Configuração de IA**
```sql
CREATE TABLE ai_provider_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  endpoint TEXT NOT NULL,
  api_key_env_var TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 1,
  max_tokens INTEGER DEFAULT 4000,
  temperature DECIMAL(3,2) DEFAULT 0.7,
  cost_per_1k_tokens DECIMAL(10,6) DEFAULT 0,
  rate_limit_per_minute INTEGER DEFAULT 60,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Configuração multi-provider para geração de questões por IA

### **5. Tabela `assessment_assets` - Assets de Avaliações**
```sql
CREATE TABLE assessment_assets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assessment_id UUID REFERENCES assessments(id) ON DELETE CASCADE,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('header_image', 'school_logo')),
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  uploaded_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Sistema de upload e gestão de imagens para personalização de avaliações

### **6. Tabela `question_feedback` - Sistema de Feedback**
```sql
CREATE TABLE question_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  feedback_type TEXT NOT NULL CHECK (feedback_type IN ('error', 'improvement', 'inappropriate', 'duplicate')),
  description TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Sistema de feedback e moderação de questões

### **7. Tabelas SEO - Sistema de SEO**
```sql
-- Configurações globais de SEO
CREATE TABLE seo_global_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  site_name TEXT DEFAULT 'Atividade Pronta',
  site_description TEXT,
  default_og_image TEXT,
  google_analytics_id TEXT,
  google_search_console_id TEXT,
  robots_txt_content TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Configurações por página
CREATE TABLE seo_page_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  page_path TEXT NOT NULL UNIQUE,
  title TEXT,
  description TEXT,
  keywords TEXT[],
  og_title TEXT,
  og_description TEXT,
  og_image TEXT,
  canonical_url TEXT,
  noindex BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Métricas de SEO
CREATE TABLE seo_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  page_path TEXT NOT NULL,
  metric_type TEXT NOT NULL,
  metric_value DECIMAL(10,2),
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Sistema completo de SEO com configurações e métricas

### **8. Tabela `admin_audit_log` - Auditoria Administrativa**
```sql
CREATE TABLE admin_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL,
  target_type TEXT,
  target_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Auditoria completa de ações administrativas

### **9. Tabela `trial_history` - Histórico de Trials**
```sql
CREATE TABLE trial_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  trial_type TEXT NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'converted', 'cancelled')),
  conversion_plan_id UUID REFERENCES plans(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```
**Propósito:** Rastreamento completo de períodos de teste

---

## 🔧 Evoluções em Tabelas Existentes

### **Tabela `questions` - Campos Adicionados:**
- `source TEXT DEFAULT 'teacher'` - Origem da questão (teacher/platform)
- `is_shared_with_school BOOLEAN DEFAULT false` - Compartilhamento escolar
- `school_id UUID` - Referência à escola
- `status TEXT DEFAULT 'active'` - Status da questão
- `is_public BOOLEAN DEFAULT false` - Questão pública

### **Tabela `assessments` - Campos Adicionados:**
- `school_id UUID` - Referência à escola
- `is_template BOOLEAN DEFAULT false` - Marcação de template
- `template_name TEXT` - Nome do template
- `font_family TEXT DEFAULT 'Helvetica'` - Família da fonte
- `custom_header TEXT` - Cabeçalho personalizado

### **Tabela `subscriptions` - Campos Adicionados:**
- `stripe_subscription_id TEXT` - ID da assinatura no Stripe
- `stripe_customer_id TEXT` - ID do cliente no Stripe
- `current_period_start TIMESTAMP` - Início do período atual
- `current_period_end TIMESTAMP` - Fim do período atual
- `cancel_at_period_end BOOLEAN DEFAULT false` - Cancelamento programado

---

## 📈 Estatísticas de Evolução

### **Crescimento do Schema:**
- **Tabelas Iniciais:** 7 tabelas
- **Tabelas Atuais:** 31 tabelas
- **Crescimento:** +343% (24 novas tabelas)

### **Evolução da Tabela `profiles`:**
- **Colunas Iniciais:** 8 colunas
- **Colunas Atuais:** 15 colunas
- **Crescimento:** +87% (7 novas colunas)

### **Funcionalidades Adicionadas:**
- ✅ Sistema de administração multi-nível
- ✅ Gestão de escolas (multi-tenant)
- ✅ Integração completa com Stripe
- ✅ Sistema de geração de questões por IA
- ✅ Sistema de feedback e moderação
- ✅ Sistema completo de SEO
- ✅ Auditoria administrativa
- ✅ Gestão de assets e personalização
- ✅ Sistema de trials e conversões

---

## 🎯 Conclusão

A evolução do schema reflete o crescimento natural da plataforma de um MVP simples para uma solução educacional completa e robusta. Todas as adições foram implementadas de forma incremental, mantendo a compatibilidade com o sistema existente e seguindo as melhores práticas de design de banco de dados.

**Status:** ✅ **Schema Evolutivo Bem Estruturado**
