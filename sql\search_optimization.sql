-- =====================================================
-- OTIMIZAÇÕES DE BUSCA PARA ATIVIDADE PRONTA
-- =====================================================
-- Este arquivo contém otimizações de índices e full-text search
-- para melhorar a performance das buscas na aplicação

-- =====================================================
-- 1. ÍNDICES FULL-TEXT SEARCH PARA QUESTÕES
-- =====================================================

-- Criar coluna de busca full-text para questões
ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Função para atualizar o vetor de busca das questões
CREATE OR REPLACE FUNCTION update_questions_search_vector()
RETURNS trigger AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('portuguese', COALESCE(NEW.enunciado, '')), 'A') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.topico, '')), 'B') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.disciplina, '')), 'B') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.subtopico, '')), 'C') ||
    setweight(to_tsvector('portuguese', COALESCE(array_to_string(NEW.tags, ' '), '')), 'C') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.explicacao, '')), 'D');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar automaticamente o vetor de busca
DROP TRIGGER IF EXISTS questions_search_vector_update ON questions;
CREATE TRIGGER questions_search_vector_update
  BEFORE INSERT OR UPDATE ON questions
  FOR EACH ROW EXECUTE FUNCTION update_questions_search_vector();

-- Atualizar vetores existentes
UPDATE questions SET search_vector = 
  setweight(to_tsvector('portuguese', COALESCE(enunciado, '')), 'A') ||
  setweight(to_tsvector('portuguese', COALESCE(topico, '')), 'B') ||
  setweight(to_tsvector('portuguese', COALESCE(disciplina, '')), 'B') ||
  setweight(to_tsvector('portuguese', COALESCE(subtopico, '')), 'C') ||
  setweight(to_tsvector('portuguese', COALESCE(array_to_string(tags, ' '), '')), 'C') ||
  setweight(to_tsvector('portuguese', COALESCE(explicacao, '')), 'D');

-- Índice GIN para busca full-text em questões
CREATE INDEX IF NOT EXISTS questions_search_vector_idx 
ON questions USING GIN (search_vector);

-- =====================================================
-- 2. ÍNDICES FULL-TEXT SEARCH PARA AVALIAÇÕES
-- =====================================================

-- Criar coluna de busca full-text para avaliações
ALTER TABLE assessments 
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Função para atualizar o vetor de busca das avaliações
CREATE OR REPLACE FUNCTION update_assessments_search_vector()
RETURNS trigger AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('portuguese', COALESCE(NEW.titulo, '')), 'A') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.disciplina, '')), 'B') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.serie, '')), 'B') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.descricao, '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar automaticamente o vetor de busca
DROP TRIGGER IF EXISTS assessments_search_vector_update ON assessments;
CREATE TRIGGER assessments_search_vector_update
  BEFORE INSERT OR UPDATE ON assessments
  FOR EACH ROW EXECUTE FUNCTION update_assessments_search_vector();

-- Atualizar vetores existentes
UPDATE assessments SET search_vector = 
  setweight(to_tsvector('portuguese', COALESCE(titulo, '')), 'A') ||
  setweight(to_tsvector('portuguese', COALESCE(disciplina, '')), 'B') ||
  setweight(to_tsvector('portuguese', COALESCE(serie, '')), 'B') ||
  setweight(to_tsvector('portuguese', COALESCE(descricao, '')), 'C');

-- Índice GIN para busca full-text em avaliações
CREATE INDEX IF NOT EXISTS assessments_search_vector_idx 
ON assessments USING GIN (search_vector);

-- =====================================================
-- 3. ÍNDICES FULL-TEXT SEARCH PARA TEMPLATES
-- =====================================================

-- Criar coluna de busca full-text para templates
ALTER TABLE templates 
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Função para atualizar o vetor de busca dos templates
CREATE OR REPLACE FUNCTION update_templates_search_vector()
RETURNS trigger AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('portuguese', COALESCE(NEW.nome, '')), 'A') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.categoria, '')), 'B') ||
    setweight(to_tsvector('portuguese', COALESCE(NEW.descricao, '')), 'C') ||
    setweight(to_tsvector('portuguese', COALESCE(array_to_string(NEW.tags, ' '), '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar automaticamente o vetor de busca
DROP TRIGGER IF EXISTS templates_search_vector_update ON templates;
CREATE TRIGGER templates_search_vector_update
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW EXECUTE FUNCTION update_templates_search_vector();

-- Atualizar vetores existentes
UPDATE templates SET search_vector = 
  setweight(to_tsvector('portuguese', COALESCE(nome, '')), 'A') ||
  setweight(to_tsvector('portuguese', COALESCE(categoria, '')), 'B') ||
  setweight(to_tsvector('portuguese', COALESCE(descricao, '')), 'C') ||
  setweight(to_tsvector('portuguese', COALESCE(array_to_string(tags, ' '), '')), 'C');

-- Índice GIN para busca full-text em templates
CREATE INDEX IF NOT EXISTS templates_search_vector_idx 
ON templates USING GIN (search_vector);

-- =====================================================
-- 4. ÍNDICES OTIMIZADOS PARA QUERIES FREQUENTES
-- =====================================================

-- Índices compostos para questões (queries mais frequentes)
CREATE INDEX IF NOT EXISTS questions_disciplina_serie_idx 
ON questions (disciplina, serie);

CREATE INDEX IF NOT EXISTS questions_topico_dificuldade_idx 
ON questions (topico, dificuldade);

CREATE INDEX IF NOT EXISTS questions_autor_status_idx 
ON questions (autor_id, status);

CREATE INDEX IF NOT EXISTS questions_public_approved_idx 
ON questions (is_public, status) WHERE status = 'approved';

-- Índices compostos para avaliações
CREATE INDEX IF NOT EXISTS assessments_disciplina_serie_idx 
ON assessments (disciplina, serie);

CREATE INDEX IF NOT EXISTS assessments_autor_public_idx 
ON assessments (autor_id, is_public);

CREATE INDEX IF NOT EXISTS assessments_public_featured_idx 
ON assessments (is_public, is_featured) WHERE is_public = true;

-- Índices para templates
CREATE INDEX IF NOT EXISTS templates_categoria_public_idx 
ON templates (categoria, is_public);

CREATE INDEX IF NOT EXISTS templates_system_premium_idx 
ON templates (is_system, is_premium);

-- Índices para profiles (busca de usuários)
CREATE INDEX IF NOT EXISTS profiles_nome_gin_idx 
ON profiles USING GIN (to_tsvector('portuguese', nome));

CREATE INDEX IF NOT EXISTS profiles_email_idx 
ON profiles (email);

CREATE INDEX IF NOT EXISTS profiles_escola_idx 
ON profiles (escola);

CREATE INDEX IF NOT EXISTS profiles_plano_active_idx 
ON profiles (plano, is_active);

-- =====================================================
-- 5. FUNÇÕES AUXILIARES PARA BUSCA
-- =====================================================

-- Função para busca inteligente de questões
CREATE OR REPLACE FUNCTION search_questions(
  search_term text,
  user_id uuid DEFAULT NULL,
  filters jsonb DEFAULT '{}'::jsonb
)
RETURNS TABLE (
  id uuid,
  enunciado text,
  disciplina text,
  serie text,
  topico text,
  dificuldade text,
  tipo text,
  rank real
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    q.id,
    q.enunciado,
    q.disciplina,
    q.serie,
    q.topico,
    q.dificuldade,
    q.tipo,
    ts_rank(q.search_vector, plainto_tsquery('portuguese', search_term)) as rank
  FROM questions q
  WHERE 
    q.search_vector @@ plainto_tsquery('portuguese', search_term)
    AND (user_id IS NULL OR q.autor_id = user_id OR q.is_public = true)
    AND q.status = 'approved'
  ORDER BY rank DESC, q.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Função para busca inteligente de avaliações
CREATE OR REPLACE FUNCTION search_assessments(
  search_term text,
  user_id uuid DEFAULT NULL,
  filters jsonb DEFAULT '{}'::jsonb
)
RETURNS TABLE (
  id uuid,
  titulo text,
  disciplina text,
  serie text,
  is_public boolean,
  rank real
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.titulo,
    a.disciplina,
    a.serie,
    a.is_public,
    ts_rank(a.search_vector, plainto_tsquery('portuguese', search_term)) as rank
  FROM assessments a
  WHERE 
    a.search_vector @@ plainto_tsquery('portuguese', search_term)
    AND (user_id IS NULL OR a.autor_id = user_id OR a.is_public = true)
  ORDER BY rank DESC, a.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. CONFIGURAÇÕES DE PERFORMANCE
-- =====================================================

-- Configurar parâmetros de full-text search para melhor performance
-- (Estas configurações devem ser aplicadas pelo administrador do banco)

-- Comentários para o DBA:
-- ALTER SYSTEM SET default_text_search_config = 'pg_catalog.portuguese';
-- ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
-- SELECT pg_reload_conf();

-- =====================================================
-- 7. VIEWS OTIMIZADAS PARA BUSCA
-- =====================================================

-- View otimizada para busca de questões públicas
CREATE OR REPLACE VIEW public_questions_search AS
SELECT 
  q.id,
  q.enunciado,
  q.disciplina,
  q.serie,
  q.topico,
  q.subtopico,
  q.dificuldade,
  q.tipo,
  q.tags,
  q.search_vector,
  p.nome as autor_nome,
  p.escola as autor_escola
FROM questions q
JOIN profiles p ON q.autor_id = p.id
WHERE q.is_public = true AND q.status = 'approved';

-- View otimizada para busca de avaliações públicas
CREATE OR REPLACE VIEW public_assessments_search AS
SELECT 
  a.id,
  a.titulo,
  a.disciplina,
  a.serie,
  a.descricao,
  a.search_vector,
  a.view_count,
  a.download_count,
  p.nome as autor_nome,
  p.escola as autor_escola
FROM assessments a
JOIN profiles p ON a.autor_id = p.id
WHERE a.is_public = true;

-- =====================================================
-- COMENTÁRIOS FINAIS
-- =====================================================

-- Para aplicar estas otimizações:
-- 1. Execute este script no Supabase SQL Editor
-- 2. Monitore a performance das queries
-- 3. Ajuste os índices conforme necessário
-- 4. Considere usar pg_stat_statements para análise de performance

-- Para reverter (se necessário):
-- DROP INDEX IF EXISTS questions_search_vector_idx;
-- DROP INDEX IF EXISTS assessments_search_vector_idx;
-- DROP INDEX IF EXISTS templates_search_vector_idx;
-- ALTER TABLE questions DROP COLUMN IF EXISTS search_vector;
-- ALTER TABLE assessments DROP COLUMN IF EXISTS search_vector;
-- ALTER TABLE templates DROP COLUMN IF EXISTS search_vector;
