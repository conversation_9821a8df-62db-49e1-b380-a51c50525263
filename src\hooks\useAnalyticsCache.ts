import { useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'

export const useAnalyticsCache = () => {
  const queryClient = useQueryClient()

  // Invalidar cache de analytics quando dados relacionados mudam
  const invalidateAnalytics = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['adminAnalytics'] })
  }, [queryClient])

  // Invalidar cache quando usuários são modificados
  const invalidateOnUserChange = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['adminAnalytics'] })
    queryClient.invalidateQueries({ queryKey: ['users'] })
    queryClient.invalidateQueries({ queryKey: ['totalUsersCount'] })
  }, [queryClient])

  // Invalidar cache quando questões são modificadas
  const invalidateOnQuestionChange = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['adminAnalytics'] })
  }, [queryClient])

  // Invalidar cache quando avaliações são modificadas
  const invalidateOnAssessmentChange = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['adminAnalytics'] })
  }, [queryClient])

  // Pré-carregar dados de analytics
  const prefetchAnalytics = useCallback((timeRange: string, userRoleFilter?: string, schoolFilter?: string) => {
    queryClient.prefetchQuery({
      queryKey: ['adminAnalytics', timeRange, userRoleFilter, schoolFilter],
      staleTime: 1000 * 60 * 5, // 5 minutos
    })
  }, [queryClient])

  // Limpar cache antigo
  const clearOldCache = useCallback(() => {
    queryClient.removeQueries({
      queryKey: ['adminAnalytics'],
      predicate: (query) => {
        const lastUpdated = query.state.dataUpdatedAt
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000)
        return lastUpdated < fiveMinutesAgo
      }
    })
  }, [queryClient])

  return {
    invalidateAnalytics,
    invalidateOnUserChange,
    invalidateOnQuestionChange,
    invalidateOnAssessmentChange,
    prefetchAnalytics,
    clearOldCache
  }
}
