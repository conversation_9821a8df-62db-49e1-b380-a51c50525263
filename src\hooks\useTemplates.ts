import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { Database } from '../types/database'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

type Template = Database['public']['Tables']['templates']['Row']
type TemplateInsert = Database['public']['Tables']['templates']['Insert']
type TemplateUpdate = Database['public']['Tables']['templates']['Update']

export const useTemplates = () => {
  const { profile } = useAuth()
  const queryClient = useQueryClient()

  const {
    data: templates = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['templates'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data as Template[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })

  // Mutation para criar template
  const createTemplateMutation = useMutation({
    mutationFn: async (templateData: Omit<TemplateInsert, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from('templates')
        .insert({
          ...templateData,
          created_by: profile?.id
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] })
      toast.success('Template criado com sucesso!')
    },
    onError: (error) => {
      console.error('Erro ao criar template:', error)
      toast.error('Erro ao criar template')
    }
  })

  // Mutation para atualizar template
  const updateTemplateMutation = useMutation({
    mutationFn: async ({ id, ...updateData }: TemplateUpdate & { id: string }) => {
      const { data, error } = await supabase
        .from('templates')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] })
      toast.success('Template atualizado com sucesso!')
    },
    onError: (error) => {
      console.error('Erro ao atualizar template:', error)
      toast.error('Erro ao atualizar template')
    }
  })

  // Mutation para deletar template
  const deleteTemplateMutation = useMutation({
    mutationFn: async (templateId: string) => {
      const { error } = await supabase
        .from('templates')
        .delete()
        .eq('id', templateId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['templates'] })
      toast.success('Template excluído com sucesso!')
    },
    onError: (error) => {
      console.error('Erro ao excluir template:', error)
      toast.error('Erro ao excluir template')
    }
  })

  return {
    templates,
    isLoading,
    error,
    createTemplate: createTemplateMutation.mutate,
    updateTemplate: updateTemplateMutation.mutate,
    deleteTemplate: deleteTemplateMutation.mutate,
    isCreating: createTemplateMutation.isPending,
    isUpdating: updateTemplateMutation.isPending,
    isDeleting: deleteTemplateMutation.isPending
  }
}