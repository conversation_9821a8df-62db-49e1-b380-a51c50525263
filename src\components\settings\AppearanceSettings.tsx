import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import { motion } from 'framer-motion'
import { useTheme } from '../../contexts/ThemeContext'

const AppearanceSettings: React.FC = () => {
  const { theme, fontSize, compactMode, setTheme, setFontSize, setCompactMode } = useTheme()

  const themeOptions = [
    { value: 'light', label: 'Claro', icon: Sun, description: 'Tema claro para uso durante o dia' },
    { value: 'dark', label: 'Escuro', icon: <PERSON>, description: 'Tema escuro para reduzir o cansaço visual' },
    { value: 'system', label: 'Sistema', icon: Monitor, description: 'Segue a configuração do sistema' }
  ]

  const fontSizeOptions = [
    { value: 'small', label: 'Pequeno', description: 'Texto menor para mais conteúdo na tela' },
    { value: 'medium', label: '<PERSON>é<PERSON>', description: 'Tamanho padrão recomendado' },
    { value: 'large', label: 'Grande', description: 'Texto maior para melhor legibilidade' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
          <Palette className="w-5 h-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Aparência</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Personalize a aparência da interface
          </p>
        </div>
      </div>

      <div className="space-y-8">
        {/* Tema */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Tema</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {themeOptions.map((option) => {
              const Icon = option.icon
              return (
                <button
                  key={option.value}
                  onClick={() => setTheme(option.value as 'light' | 'dark' | 'system')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    theme === option.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex flex-col items-center space-y-2">
                    <Icon className={`w-6 h-6 ${
                      theme === option.value 
                        ? 'text-blue-600 dark:text-blue-400' 
                        : 'text-gray-600 dark:text-gray-400'
                    }`} />
                    <div className="text-center">
                      <p className={`text-sm font-medium ${
                        theme === option.value 
                          ? 'text-blue-900 dark:text-blue-100' 
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {option.label}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {option.description}
                      </p>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Tamanho da Fonte */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Tamanho da Fonte</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {fontSizeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setFontSize(option.value as 'small' | 'medium' | 'large')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  fontSize === option.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="text-center">
                  <p className={`font-medium ${
                    option.value === 'small' ? 'text-sm' : 
                    option.value === 'medium' ? 'text-base' : 'text-lg'
                  } ${
                    fontSize === option.value 
                      ? 'text-blue-900 dark:text-blue-100' 
                      : 'text-gray-900 dark:text-white'
                  }`}>
                    {option.label}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {option.description}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Modo Compacto */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Layout</h4>
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                Modo Compacto
              </h5>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Reduz o espaçamento entre elementos para mostrar mais conteúdo
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={compactMode}
                onChange={(e) => setCompactMode(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Preview */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Preview</h4>
          <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className={`space-y-2 ${compactMode ? 'space-y-1' : 'space-y-2'}`}>
              <h5 className={`font-medium text-gray-900 dark:text-white ${
                fontSize === 'small' ? 'text-sm' : 
                fontSize === 'medium' ? 'text-base' : 'text-lg'
              }`}>
                Exemplo de Título
              </h5>
              <p className={`text-gray-600 dark:text-gray-400 ${
                fontSize === 'small' ? 'text-xs' : 
                fontSize === 'medium' ? 'text-sm' : 'text-base'
              }`}>
                Este é um exemplo de como o texto aparecerá com suas configurações atuais.
              </p>
              <div className={`flex space-x-2 ${compactMode ? 'mt-1' : 'mt-2'}`}>
                <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded text-xs">
                  Tag exemplo
                </span>
                <span className="px-2 py-1 bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 rounded text-xs">
                  Outra tag
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default AppearanceSettings
