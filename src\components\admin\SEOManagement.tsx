import React, { useState } from 'react'
import {
  Search,
  Globe,
  RefreshCw,
  Download,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Loader2,
  Save,
  TrendingUp,
  Eye
} from 'lucide-react'

import { useSitemap } from '../../hooks/useSitemap'
import { useSEOSettings, useSEOMetrics, useSEOValidation } from '../../hooks/useSEOSettings'
// DESATIVADO: import { usePerformanceMonitor } from '../../hooks/usePerformanceMonitor'
import SEODashboard from './SEODashboard'
import toast from 'react-hot-toast'

/**
 * Componente para gerenciar SEO e sitemap no painel admin
 */
const SEOManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'sitemap' | 'meta' | 'performance'>('dashboard')
  const [sitemapContent, setSitemapContent] = useState<string>('')
  
  const {
    generateSitemap,
    updateSitemapCache,
    getSitemapFromCache,
    isGenerating,
    error
  } = useSitemap()

  // Hooks para configurações SEO
  const {
    globalSettings,
    pageSettings,
    saveGlobalSettings,
    savePageSettings,
    isLoadingGlobal,
    isLoadingPages,
    isSavingGlobal,
    isSavingPage
  } = useSEOSettings()

  // Hooks para métricas de performance
  const {
    metrics,
    getAverageMetrics,
    isLoading: isLoadingMetrics
  } = useSEOMetrics()

  // Hook para validação
  const {
    validatePageSettings,
    hasErrors,
    hasWarnings,
    validationResults
  } = useSEOValidation()

  // DESATIVADO: Hook para monitoramento de performance
  // const { metrics: webVitalsMetrics } = usePerformanceMonitor()

  // Estados locais para formulários
  const [editingGlobalSettings, setEditingGlobalSettings] = useState(false)
  const [globalFormData, setGlobalFormData] = useState<any>({})
  const [selectedPageSettings, setSelectedPageSettings] = useState<any>(null)

  // Sincronizar dados carregados com formulário
  React.useEffect(() => {
    if (globalSettings) {
      setGlobalFormData(globalSettings)
    }
  }, [globalSettings])

  // Handler para salvar configurações globais
  const handleSaveGlobalSettings = async () => {
    try {
      await saveGlobalSettings.mutateAsync(globalFormData)
    } catch (error) {
      console.error('Erro ao salvar configurações globais:', error)
    }
  }

  // Função para obter status da métrica
  const getMetricStatus = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return { status: 'good', color: 'green' }
    if (value <= thresholds.poor) return { status: 'needs-improvement', color: 'yellow' }
    return { status: 'poor', color: 'red' }
  }

  // Função para calcular porcentagem da barra de progresso
  const getProgressPercentage = (value: number, max: number) => {
    return Math.min((value / max) * 100, 100)
  }

  // Handle sitemap generation
  const handleGenerateSitemap = async () => {
    const sitemap = await generateSitemap()
    if (sitemap) {
      setSitemapContent(sitemap)
      toast.success('Sitemap gerado com sucesso!')
    } else {
      toast.error('Erro ao gerar sitemap')
    }
  }

  // Handle sitemap cache update
  const handleUpdateCache = async () => {
    const success = await updateSitemapCache()
    if (success) {
      toast.success('Cache do sitemap atualizado!')
    } else {
      toast.error('Erro ao atualizar cache')
    }
  }

  // Handle get sitemap from cache
  const handleGetFromCache = async () => {
    const sitemap = await getSitemapFromCache()
    if (sitemap) {
      setSitemapContent(sitemap)
      toast.success('Sitemap carregado do cache!')
    } else {
      toast.error('Nenhum sitemap encontrado no cache')
    }
  }

  // Download sitemap
  const handleDownloadSitemap = () => {
    if (!sitemapContent) {
      toast.error('Nenhum sitemap para download')
      return
    }

    const blob = new Blob([sitemapContent], { type: 'application/xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sitemap.xml'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('Sitemap baixado!')
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento SEO
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Gerencie sitemap, meta tags e performance SEO
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <a
            href="/sitemap.xml"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            <span>Ver Sitemap Atual</span>
          </a>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: TrendingUp },
            { id: 'sitemap', label: 'Sitemap', icon: Globe },
            { id: 'meta', label: 'Meta Tags', icon: Search },
            { id: 'performance', label: 'Performance', icon: RefreshCw }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        {activeTab === 'dashboard' && (
          <SEODashboard />
        )}

        {activeTab === 'sitemap' && (
          <div className="p-6">
            <div className="space-y-6">
              {/* Actions */}
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleGenerateSitemap}
                  disabled={isGenerating}
                  className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  <span>Gerar Sitemap</span>
                </button>

                <button
                  onClick={handleUpdateCache}
                  disabled={isGenerating}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <CheckCircle className="w-4 h-4" />
                  )}
                  <span>Atualizar Cache</span>
                </button>

                <button
                  onClick={handleGetFromCache}
                  disabled={isGenerating}
                  className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Globe className="w-4 h-4" />
                  )}
                  <span>Carregar do Cache</span>
                </button>

                {sitemapContent && (
                  <button
                    onClick={handleDownloadSitemap}
                    className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span>Baixar XML</span>
                  </button>
                )}
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 dark:text-red-300">{error}</span>
                </div>
              )}

              {/* Sitemap Preview */}
              {sitemapContent && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Preview do Sitemap
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                      {sitemapContent}
                    </pre>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    URLs encontradas: {(sitemapContent.match(/<url>/g) || []).length}
                  </div>
                </div>
              )}

              {/* Instructions */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Como usar:
                </h4>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>1. <strong>Gerar Sitemap:</strong> Cria um novo sitemap com todas as URLs públicas</li>
                  <li>2. <strong>Atualizar Cache:</strong> Salva o sitemap no banco de dados para acesso rápido</li>
                  <li>3. <strong>Carregar do Cache:</strong> Recupera o último sitemap salvo</li>
                  <li>4. <strong>Baixar XML:</strong> Faz download do arquivo XML para upload manual</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'meta' && (
          <div className="p-6">
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Gerenciamento de Meta Tags
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Configure meta tags globais e específicas para páginas públicas
                  </p>
                </div>
              </div>

              {/* Global Meta Tags */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                  Meta Tags Globais
                </h4>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Site Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Título do Site
                    </label>
                    <input
                      type="text"
                      value={globalFormData.site_title || ''}
                      onChange={(e) => {
                        const newValue = e.target.value
                        setGlobalFormData((prev: any) => ({ ...prev, site_title: newValue }))
                      }}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Título principal do site"
                    />
                    <div className="mt-1 flex items-center justify-between">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Usado como título padrão em todas as páginas
                      </p>
                      <span className={`text-xs ${
                        globalFormData.site_title?.length > 60 ? 'text-red-500' :
                        globalFormData.site_title?.length > 50 ? 'text-yellow-500' :
                        'text-gray-500'
                      }`}>
                        {globalFormData.site_title?.length || 0}/60
                      </span>
                    </div>
                    {(() => {
                      const titleValidation = validateTitle(globalFormData.site_title || '')
                      if (!titleValidation.isValid) {
                        return (
                          <p className={`text-xs mt-1 ${
                            titleValidation.severity === 'error' ? 'text-red-500' : 'text-yellow-500'
                          }`}>
                            {titleValidation.message}
                          </p>
                        )
                      }
                      return null
                    })()}
                  </div>

                  {/* Site Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Descrição do Site
                    </label>
                    <textarea
                      rows={3}
                      value={globalFormData.site_description || ''}
                      onChange={(e) => setGlobalFormData((prev: any) => ({ ...prev, site_description: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Descrição principal do site"
                    />
                    <div className="mt-1 flex items-center justify-between">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Descrição padrão para mecanismos de busca
                      </p>
                      <span className={`text-xs ${
                        globalFormData.site_description?.length > 160 ? 'text-red-500' :
                        globalFormData.site_description?.length > 150 ? 'text-yellow-500' :
                        'text-gray-500'
                      }`}>
                        {globalFormData.site_description?.length || 0}/160
                      </span>
                    </div>
                    {(() => {
                      const descValidation = validateDescription(globalFormData.site_description || '')
                      if (!descValidation.isValid) {
                        return (
                          <p className={`text-xs mt-1 ${
                            descValidation.severity === 'error' ? 'text-red-500' : 'text-yellow-500'
                          }`}>
                            {descValidation.message}
                          </p>
                        )
                      }
                      return null
                    })()}
                  </div>

                  {/* Keywords */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Palavras-chave Globais
                    </label>
                    <input
                      type="text"
                      value={globalFormData.global_keywords?.join(', ') || ''}
                      onChange={(e) => setGlobalFormData((prev: any) => ({
                        ...prev,
                        global_keywords: e.target.value.split(',').map((k: string) => k.trim()).filter(Boolean)
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Palavras-chave separadas por vírgula"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Palavras-chave principais do site
                    </p>
                  </div>

                  {/* OG Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Imagem Open Graph
                    </label>
                    <input
                      type="url"
                      value={globalFormData.og_image_url || ''}
                      onChange={(e) => setGlobalFormData((prev: any) => ({ ...prev, og_image_url: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="URL da imagem para redes sociais"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Imagem exibida ao compartilhar nas redes sociais
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={handleSaveGlobalSettings}
                    disabled={isSavingGlobal}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
                  >
                    {isSavingGlobal ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>Salvar Meta Tags Globais</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Page-specific Meta Tags */}
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                  Meta Tags por Página
                </h4>

                {isLoadingPages ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                    <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando páginas...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pageSettings?.map((page) => (
                      <div key={page.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium text-gray-900 dark:text-white">{page.page_name}</h5>
                          <div className="flex items-center space-x-2">
                            <span className={`text-xs px-2 py-1 rounded ${
                              page.is_active
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400'
                                : 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-400'
                            }`}>
                              {page.is_active ? 'Ativo' : 'Inativo'}
                            </span>
                            <button
                              onClick={() => setSelectedPageSettings(page)}
                              className="text-blue-600 hover:text-blue-700 text-sm"
                            >
                              Editar
                            </button>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Título:</span>
                            <p className="text-gray-900 dark:text-white">{page.title || 'Não definido'}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">URL:</span>
                            <p className="text-gray-900 dark:text-white">{page.canonical_url || page.page_path}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Descrição:</span>
                            <p className="text-gray-900 dark:text-white text-xs">
                              {page.description ?
                                (page.description.length > 100 ?
                                  `${page.description.substring(0, 100)}...` :
                                  page.description
                                ) :
                                'Não definida'
                              }
                            </p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Keywords:</span>
                            <p className="text-gray-900 dark:text-white text-xs">
                              {page.keywords?.length ? page.keywords.join(', ') : 'Não definidas'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Editar Meta Tags Específicas
                  </button>
                </div>
              </div>

              {/* SEO Tools */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Ferramentas SEO
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <Search className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">Análise de Títulos</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Verifique se os títulos estão otimizados</p>
                  </div>
                  <div className="text-center">
                    <Globe className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">Teste Open Graph</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Visualize como aparece nas redes sociais</p>
                  </div>
                  <div className="text-center">
                    <RefreshCw className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">Validação Schema</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Teste dados estruturados</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="p-6">
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Análise de Performance SEO
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Monitore Core Web Vitals e performance das páginas públicas
                  </p>
                </div>
                <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>Atualizar Métricas</span>
                </button>
              </div>

              {/* Core Web Vitals */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* LCP - Largest Contentful Paint */}
                {(() => {
                  // DESATIVADO: const lcpValue = webVitalsMetrics?.lcp || 1800 // fallback para 1.8s
                  const lcpValue = 0 // Performance monitoring desativado
                  const lcpSeconds = lcpValue / 1000
                  const lcpStatus = 'neutral' // getMetricStatus(lcpSeconds, { good: 2.5, poor: 4.0 })
                  const lcpProgress = 0 // getProgressPercentage(lcpSeconds, 4.0)

                  return (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">LCP</h4>
                        <div className={`w-3 h-3 bg-${lcpStatus.color}-500 rounded-full`}></div>
                      </div>
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                        {lcpSeconds.toFixed(1)}s
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Largest Contentful Paint</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className={`bg-${lcpStatus.color}-500 h-2 rounded-full`} style={{ width: `${100 - lcpProgress}%` }}></div>
                      </div>
                      <p className={`text-xs text-${lcpStatus.color}-600 dark:text-${lcpStatus.color}-400 mt-2`}>
                        {lcpStatus.status === 'good' ? 'Bom' : lcpStatus.status === 'needs-improvement' ? 'Precisa melhorar' : 'Ruim'} (&lt; 2.5s)
                      </p>
                    </div>
                  )
                })()}

                {/* FID - First Input Delay */}
                {(() => {
                  // DESATIVADO: const fidValue = webVitalsMetrics?.fid || 85 // fallback para 85ms
                  const fidValue = 0 // Performance monitoring desativado
                  const fidStatus = 'neutral' // getMetricStatus(fidValue, { good: 100, poor: 300 })
                  const fidProgress = 0 // getProgressPercentage(fidValue, 300)

                  return (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">FID</h4>
                        <div className={`w-3 h-3 bg-${fidStatus.color}-500 rounded-full`}></div>
                      </div>
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                        {Math.round(fidValue)}ms
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">First Input Delay</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className={`bg-${fidStatus.color}-500 h-2 rounded-full`} style={{ width: `${100 - fidProgress}%` }}></div>
                      </div>
                      <p className={`text-xs text-${fidStatus.color}-600 dark:text-${fidStatus.color}-400 mt-2`}>
                        {fidStatus.status === 'good' ? 'Bom' : fidStatus.status === 'needs-improvement' ? 'Precisa melhorar' : 'Ruim'} (&lt; 100ms)
                      </p>
                    </div>
                  )
                })()}

                {/* CLS - Cumulative Layout Shift */}
                {(() => {
                  // DESATIVADO: const clsValue = webVitalsMetrics?.cls || 0.15 // fallback para 0.15
                  const clsValue = 0 // Performance monitoring desativado
                  const clsStatus = 'neutral' // getMetricStatus(clsValue, { good: 0.1, poor: 0.25 })
                  const clsProgress = 0 // getProgressPercentage(clsValue, 0.25)

                  return (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">CLS</h4>
                        <div className={`w-3 h-3 bg-${clsStatus.color}-500 rounded-full`}></div>
                      </div>
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                        {clsValue.toFixed(3)}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Cumulative Layout Shift</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className={`bg-${clsStatus.color}-500 h-2 rounded-full`} style={{ width: `${clsProgress}%` }}></div>
                      </div>
                      <p className={`text-xs text-${clsStatus.color}-600 dark:text-${clsStatus.color}-400 mt-2`}>
                        {clsStatus.status === 'good' ? 'Bom' : clsStatus.status === 'needs-improvement' ? 'Precisa melhorar' : 'Ruim'} (&lt; 0.1)
                      </p>
                    </div>
                  )
                })()}
              </div>

              {/* Page Performance */}
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                  Performance por Página
                </h4>

                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Página</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Score</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">LCP</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">FID</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">CLS</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-gray-100 dark:border-gray-700">
                        <td className="py-3 px-4 text-gray-900 dark:text-white">Página Inicial</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                            92
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">1.6s</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">78ms</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">0.08</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                            Bom
                          </span>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 dark:border-gray-700">
                        <td className="py-3 px-4 text-gray-900 dark:text-white">Lista de Avaliações</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                            78
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">2.1s</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">95ms</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">0.18</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400">
                            Precisa melhorar
                          </span>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 dark:border-gray-700">
                        <td className="py-3 px-4 text-gray-900 dark:text-white">Detalhes da Avaliação</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                            88
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">1.9s</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">82ms</td>
                        <td className="py-3 px-4 text-gray-600 dark:text-gray-400">0.12</td>
                        <td className="py-3 px-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                            Bom
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* SEO Performance Insights */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recommendations */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    Recomendações de Melhoria
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Otimizar imagens</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Comprimir imagens pode melhorar LCP em 0.3s</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Reduzir layout shifts</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Definir dimensões de imagens para melhorar CLS</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Cache configurado</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Sistema de cache funcionando corretamente</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Mobile Performance */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    Performance Mobile
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Score Mobile</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">82</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '82%' }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Score Desktop</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">94</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '94%' }}></div>
                      </div>
                    </div>

                    <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Última atualização: há 2 horas
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Tools */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Ferramentas de Performance
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <RefreshCw className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">PageSpeed Insights</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Análise detalhada do Google</p>
                  </div>
                  <div className="text-center">
                    <Globe className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">GTmetrix</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Relatório de performance</p>
                  </div>
                  <div className="text-center">
                    <Search className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">Lighthouse</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Auditoria completa</p>
                  </div>
                  <div className="text-center">
                    <AlertCircle className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-blue-900 dark:text-blue-100 text-sm">Web Vitals</h5>
                    <p className="text-xs text-blue-800 dark:text-blue-200">Monitoramento contínuo</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SEOManagement
