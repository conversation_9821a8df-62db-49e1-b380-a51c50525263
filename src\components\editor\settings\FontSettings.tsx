import React from 'react'
import { Type } from 'lucide-react'
import { AssessmentConfig } from '../../../types/assessment'

interface FontSettingsProps {
  config: AssessmentConfig
  onConfigChange: (config: AssessmentConfig) => void
}

const FONT_FAMILIES = [
  { value: 'Helvetica', label: 'Helvetica', description: 'Fonte moderna e limpa' },
  { value: 'Times', label: 'Times New Roman', description: 'Fonte clássica com serifa' },
  { value: 'Courier', label: 'Courier New', description: 'Fonte monoespaçada' }
]

const FONT_SIZES = [
  { value: 10, label: '<PERSON><PERSON> (10pt)' },
  { value: 11, label: '<PERSON><PERSON>que<PERSON> (11pt)' },
  { value: 12, label: 'Normal (12pt)' },
  { value: 14, label: 'Grande (14pt)' },
  { value: 16, label: 'Muito Grande (16pt)' }
]

const FontSettings: React.FC<FontSettingsProps> = ({
  config,
  onConfigChange
}) => {
  const handlePdfOptionChange = (field: string, value: any) => {
    onConfigChange({
      ...config,
      pdfOptions: {
        ...config.pdfOptions,
        [field]: value
      }
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
          <Type className="w-5 h-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Configurações de Fonte
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Personalize a tipografia do seu documento
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Família da Fonte */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Família da Fonte
          </label>
          <div className="space-y-3">
            {FONT_FAMILIES.map((font) => (
              <label key={font.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="fontFamily"
                  value={font.value}
                  checked={config.pdfOptions.fontFamily === font.value}
                  onChange={(e) => handlePdfOptionChange('fontFamily', e.target.value)}
                  className="mt-1 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span 
                      className="text-sm font-medium text-gray-900 dark:text-white"
                      style={{ fontFamily: font.value }}
                    >
                      {font.label}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {font.description}
                  </p>
                  <div 
                    className="text-sm text-gray-700 dark:text-gray-300 mt-2 p-2 bg-gray-50 dark:bg-gray-700/50 rounded"
                    style={{ fontFamily: font.value }}
                  >
                    Exemplo de texto nesta fonte
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Tamanho da Fonte */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Tamanho da Fonte
          </label>
          <div className="space-y-2">
            {FONT_SIZES.map((size) => (
              <label key={size.value} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="fontSize"
                  value={size.value}
                  checked={config.pdfOptions.fontSize === size.value}
                  onChange={(e) => handlePdfOptionChange('fontSize', parseInt(e.target.value))}
                  className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                />
                <span className="text-sm text-gray-900 dark:text-white">
                  {size.label}
                </span>
              </label>
            ))}
          </div>

          {/* Preview do tamanho */}
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">Preview:</p>
            <p 
              className="text-gray-900 dark:text-white"
              style={{ 
                fontSize: `${config.pdfOptions.fontSize}pt`,
                fontFamily: config.pdfOptions.fontFamily 
              }}
            >
              Este é um exemplo de como o texto aparecerá no PDF
            </p>
          </div>
        </div>
      </div>

      {/* Configurações Adicionais */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Configurações Adicionais
        </h4>
        
        <div className="space-y-4">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={config.pdfOptions.boldHeaders || false}
              onChange={(e) => handlePdfOptionChange('boldHeaders', e.target.checked)}
              className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
            />
            <div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Cabeçalhos em Negrito
              </span>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Aplica formatação em negrito aos títulos e cabeçalhos
              </p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={config.pdfOptions.justifyText || false}
              onChange={(e) => handlePdfOptionChange('justifyText', e.target.checked)}
              className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
            />
            <div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Texto Justificado
              </span>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Alinha o texto nas margens esquerda e direita
              </p>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={config.pdfOptions.increasedLineSpacing || false}
              onChange={(e) => handlePdfOptionChange('increasedLineSpacing', e.target.checked)}
              className="text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 rounded"
            />
            <div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Espaçamento Aumentado
              </span>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Aumenta o espaçamento entre linhas para melhor legibilidade
              </p>
            </div>
          </label>
        </div>
      </div>
    </div>
  )
}

export default FontSettings
