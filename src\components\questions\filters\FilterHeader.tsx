import React from 'react'
import { Filter, X } from 'lucide-react'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
}

interface FilterHeaderProps {
  filters: QuestionFilters
  onClearFilters: () => void
  hasActiveFilters: boolean
}

const FilterHeader: React.FC<FilterHeaderProps> = ({
  filters,
  onClearFilters,
  hasActiveFilters
}) => {
  const activeFilterCount = Object.values(filters).filter(
    v => v && v !== '' && v !== 'all' && (!Array.isArray(v) || v.length > 0)
  ).length

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Filtros</h3>
        {hasActiveFilters && (
          <span className="bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs px-2 py-0.5 rounded-full">
            {activeFilterCount}
          </span>
        )}
      </div>
      {hasActiveFilters && (
        <button
          onClick={onClearFilters}
          className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 flex items-center space-x-1 transition-colors px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <X className="w-3 h-3" />
          <span>Limpar</span>
        </button>
      )}
    </div>
  )
}

export default FilterHeader
