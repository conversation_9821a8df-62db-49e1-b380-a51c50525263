import React from 'react'
import { DISCIPLINAS, SERIES } from '../../../constants/educationOptions'

interface QuestionFilters {
  disciplina?: string
  serie?: string
  dificuldade?: string
  tipo?: string
  competencia_bncc?: string
  search?: string
  tags?: string[]
  autor_id?: string
  is_verified?: boolean
  is_public?: boolean
  created_after?: string
  created_before?: string
  visibility_filter?: 'all' | 'private' | 'public' | 'school' | 'my_pending_or_rejected'
  status?: 'pending' | 'approved' | 'rejected' | 'all'
}

interface SubjectFilterProps {
  filters: QuestionFilters
  onFilterChange: (key: keyof QuestionFilters, value: string | string[] | boolean) => void
}

// Criar estrutura de disciplinas usando as constantes centralizadas
const DISCIPLINAS_MAP = DISCIPLINAS.reduce((acc, disciplina) => {
  acc[disciplina] = {
    series: SERIES,
    topicos: {}
  }
  return acc
}, {} as Record<string, { series: readonly string[], topicos: {} }>)

const SubjectFilter: React.FC<SubjectFilterProps> = ({
  filters,
  onFilterChange
}) => {
  const selectedDisciplina = filters.disciplina
  const disciplinaData = selectedDisciplina ? DISCIPLINAS_MAP[selectedDisciplina as keyof typeof DISCIPLINAS_MAP] : null

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      {/* Disciplina */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Disciplina
        </label>
        <select
          value={filters.disciplina || ''}
          onChange={(e) => {
            onFilterChange('disciplina', (e.target as HTMLSelectElement).value)
            onFilterChange('serie', '') // Clear série when disciplina changes
          }}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por disciplina"
          aria-label="Filtrar por disciplina"
        >
          <option value="">Todas</option>
          {Object.keys(DISCIPLINAS_MAP).map((disciplina) => (
            <option key={disciplina} value={disciplina}>
              {disciplina}
            </option>
          ))}
        </select>
      </div>

      {/* Série */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          Série
        </label>
        <select
          value={filters.serie || ''}
          onChange={(e) => onFilterChange('serie', (e.target as HTMLSelectElement).value)}
          className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white min-h-[44px]"
          title="Filtrar por série"
          aria-label="Filtrar por série"
          disabled={!disciplinaData}
        >
          <option value="">Todas</option>
          {disciplinaData?.series.map((serie) => (
            <option key={serie} value={serie}>
              {serie}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}

export default SubjectFilter
