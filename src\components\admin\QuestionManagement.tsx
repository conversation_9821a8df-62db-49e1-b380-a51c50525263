import React, { useState, useCallback, useMemo, useEffect } from 'react'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  EyeOff,
  Plus,
  Loader,
  Save,
  RefreshCw,
  X,
  Trash,
  Users,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useQuestions } from '../../hooks/useQuestions'
import { supabase } from '../../lib/supabase'
import { Database } from '../../types/database'
import toast from 'react-hot-toast'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '../../contexts/AuthContext'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'
import QuestionDetailModal from '../editor/QuestionDetailModal'

type Question = Database['public']['Tables']['questions']['Row']
type QuestionInsert = Database['public']['Tables']['questions']['Insert']
type QuestionUpdate = Database['public']['Tables']['questions']['Update']

// Usar constantes centralizadas
const disciplinasDisponiveis = DISCIPLINAS;
const seriesDisponiveis = SERIES;

interface QuestionCreateModalProps {
  onClose: () => void
  onSave: (questionData: Omit<QuestionInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at' | 'source'>) => Promise<void>
  isLoading: boolean
}

const QuestionCreateModal: React.FC<QuestionCreateModalProps> = ({ onClose, onSave, isLoading }) => {
  const { user } = useAuth()
  const [newQuestionData, setNewQuestionData] = useState<Omit<QuestionInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at' | 'source'>>({
    enunciado: '',
    disciplina: disciplinasDisponiveis[0], // Padrão
    serie: seriesDisponiveis[0], // Padrão
    topico: '',
    tipo: 'multipla_escolha',
    dificuldade: 'Médio',
    alternativas: [],
    resposta_correta: '',
    explicacao: '', // Campo obrigatório adicionado
    autor_id: user?.id || '', // Campo obrigatório adicionado
    is_public: false,
    is_verified: false,
    tags: [],
  })

  const [alternatives, setAlternatives] = useState<{ text: string; is_correct: boolean }[]>([]);
  const [currentAlternativeText, setCurrentAlternativeText] = useState('');
  const [vfCorrectAnswer, setVfCorrectAnswer] = useState<'Verdadeiro' | 'Falso' | ''>('');
  const [essayAnswer, setEssayAnswer] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;

    setNewQuestionData(prev => {
      const updatedData = {
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      };

      if (name === 'tipo') {
        setAlternatives([]);
        setCurrentAlternativeText('');
        setVfCorrectAnswer('');
        setEssayAnswer('');
      }
      return updatedData;
    });
  }

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsArray = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    setNewQuestionData(prev => ({ ...prev, tags: tagsArray }))
  }

  const handleAddAlternative = () => {
    if (currentAlternativeText.trim() !== '') {
      setAlternatives(prev => [...prev, { text: currentAlternativeText.trim(), is_correct: false }]);
      setCurrentAlternativeText('');
    }
  };

  const handleRemoveAlternative = (indexToRemove: number) => {
    setAlternatives(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleSetCorrectAlternative = (indexToSetCorrect: number) => {
    setAlternatives(prev =>
      prev.map((alt, index) => ({
        ...alt,
        is_correct: index === indexToSetCorrect,
      }))
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let finalAlternativas: string[] | null = null;
    let finalRespostaCorreta: string = '';

    if (!newQuestionData.enunciado || !newQuestionData.disciplina || !newQuestionData.serie || !newQuestionData.topico) {
      toast.error('Por favor, preencha os campos obrigatórios: Enunciado, Disciplina, Série e Tópico.');
      return;
    }

    switch (newQuestionData.tipo) {
      case 'multipla_escolha':
        const correctAlternative = alternatives.find(alt => alt.is_correct);
        if (alternatives.length === 0) {
          toast.error('Para Questões de Múltipla Escolha, adicione pelo menos uma alternativa.');
          return;
        }
        if (!correctAlternative) {
          toast.error('Para Questões de Múltipla Escolha, selecione a alternativa correta.');
          return;
        }
        finalAlternativas = alternatives.map(alt => alt.text);
        finalRespostaCorreta = correctAlternative.text;
        break;
      case 'verdadeiro_falso':
        if (!vfCorrectAnswer) {
          toast.error('Para Questões de Verdadeiro ou Falso, selecione a resposta correta.');
          return;
        }
        finalAlternativas = ['Verdadeiro', 'Falso'];
        finalRespostaCorreta = vfCorrectAnswer;
        break;
      case 'dissertativa':
        if (!essayAnswer.trim()) {
          toast.error('Para Questões Discursivas, preencha a resposta esperada.');
          return;
        }
        finalAlternativas = null;
        finalRespostaCorreta = essayAnswer.trim();
        break;
      default:
        toast.error('Tipo de questão inválido.');
        return;
    }

    const questionToSave: Omit<QuestionInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at'> = {
      ...newQuestionData,
      autor_id: user?.id,
      source: 'teacher',
      created_at: new Date().toISOString(),
      last_modified_at: new Date().toISOString(),
    };

    await onSave(questionToSave)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-2xl relative max-h-[90vh] overflow-y-auto"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Criar Nova Questão</h2>
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="enunciado" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Enunciado da Questão</label>
            <textarea
              id="enunciado"
              name="enunciado"
              value={newQuestionData.enunciado}
              onChange={handleChange}
              rows={5}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
              placeholder="Digite o enunciado da questão"
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="disciplina" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Disciplina</label>
              <select
                id="disciplina"
                name="disciplina"
                value={newQuestionData.disciplina}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                title="Selecione a disciplina da questão"
              >
                {disciplinasDisponiveis.map(d => <option key={d} value={d}>{d}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="serie" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Série/Nível</label>
              <select
                id="serie"
                name="serie"
                value={newQuestionData.serie}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                title="Selecione a série ou nível da questão"
              >
                {seriesDisponiveis.map(s => <option key={s} value={s}>{s}</option>)}
              </select>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="topico" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tópico</label>
              <input
                type="text"
                id="topico"
                name="topico"
                value={newQuestionData.topico}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                placeholder="Digite o tópico da questão"
              />
            </div>
            <div>
              <label htmlFor="subtopico" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Subtópico (Opcional)</label>
              <input
                type="text"
                id="subtopico"
                name="subtopico"
                value={newQuestionData.subtopico || ''}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                placeholder="Digite o subtópico da questão"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="tipo" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Questão</label>
              <select
                id="tipo"
                name="tipo"
                value={newQuestionData.tipo}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                title="Selecione o tipo da questão"
              >
                <option value="multipla_escolha">Múltipla Escolha</option>
                <option value="verdadeiro_falso">Verdadeiro ou Falso</option>
                <option value="dissertativa">Discursiva</option>
              </select>
            </div>
            <div>
              <label htmlFor="dificuldade" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Dificuldade</label>
              <select
                id="dificuldade"
                name="dificuldade"
                value={newQuestionData.dificuldade}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                title="Selecione a dificuldade da questão"
              >
                <option value="Fácil">Fácil</option>
                <option value="Médio">Médio</option>
                <option value="Difícil">Difícil</option>
              </select>
            </div>
          </div>

          {newQuestionData.tipo === 'multipla_escolha' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Alternativas</label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  value={currentAlternativeText}
                  onChange={(e) => setCurrentAlternativeText(e.target.value)}
                  placeholder="Nova alternativa"
                  className="flex-grow mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                />
                <button
                  type="button"
                  onClick={handleAddAlternative}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  Adicionar
                </button>
              </div>
              <div className="space-y-2">
                {alternatives.map((alt, index) => (
                  <div key={index} className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700 p-2 rounded-md">
                    <input
                      type="radio"
                      name="correct_alternative"
                      checked={alt.is_correct}
                      onChange={() => handleSetCorrectAlternative(index)}
                      className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-600"
                    />
                    <span className="flex-grow text-gray-900 dark:text-white">{alt.text}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveAlternative(index)}
                      className="p-1 rounded-full text-red-600 hover:bg-red-100 dark:hover:bg-red-700 transition-colors"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {newQuestionData.tipo === 'verdadeiro_falso' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Resposta Correta (Verdadeiro ou Falso)</label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="vf_correct_answer"
                    value="Verdadeiro"
                    checked={vfCorrectAnswer === 'Verdadeiro'}
                    onChange={() => setVfCorrectAnswer('Verdadeiro')}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="ml-2 text-gray-900 dark:text-white">Verdadeiro</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="vf_correct_answer"
                    value="Falso"
                    checked={vfCorrectAnswer === 'Falso'}
                    onChange={() => setVfCorrectAnswer('Falso')}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="ml-2 text-gray-900 dark:text-white">Falso</span>
                </label>
              </div>
            </div>
          )}

          {newQuestionData.tipo === 'dissertativa' && (
            <div>
              <label htmlFor="essay_answer" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Resposta Esperada</label>
              <textarea
                id="essay_answer"
                name="essay_answer"
                value={essayAnswer}
                onChange={(e) => setEssayAnswer(e.target.value)}
                rows={5}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
                placeholder="Digite a resposta esperada para a questão"
              />
            </div>
          )}

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tags (Separadas por vírgula)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={newQuestionData.tags?.join(', ') || ''}
              onChange={handleTagsChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              placeholder="Digite as tags da questão"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_public_create"
              name="is_public"
              checked={newQuestionData.is_public || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_public_create" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">Disponibilizar publicamente</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_verified_create"
              name="is_verified"
              checked={newQuestionData.is_verified || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_verified_create" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">Questão Verificada</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Criando...' : 'Criar Questão'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

interface QuestionEditModalProps {
  onClose: () => void;
  onSave: (questionId: string, questionData: Omit<QuestionUpdate, 'last_modified_at' | 'source'>) => Promise<void>;
  isLoading: boolean;
  question: Question;
}

const QuestionEditModal: React.FC<QuestionEditModalProps> = ({ onClose, onSave, isLoading, question }) => {
  const { user } = useAuth()
  const [editedQuestionData, setEditedQuestionData] = useState<Omit<QuestionUpdate, 'last_modified_at' | 'source'>>({
    enunciado: question.enunciado,
    disciplina: question.disciplina,
    serie: question.serie,
    topico: question.topico,
    subtopico: question.subtopico || '',
    tipo: question.tipo,
    dificuldade: question.dificuldade,
    alternativas: question.alternativas || [],
    resposta_correta: question.resposta_correta || '',
    is_public: question.is_public,
    is_verified: question.is_verified,
    tags: question.tags || [],
  })

  const [alternatives, setAlternatives] = useState<{ text: string; is_correct: boolean }[]>(() => {
    if (question.tipo === 'multipla_escolha' && question.alternativas && question.alternativas.length > 0) {
      return question.alternativas.map(altText => ({
        text: altText,
        is_correct: altText === question.resposta_correta,
      }));
    }
    return [];
  });
  const [currentAlternativeText, setCurrentAlternativeText] = useState('');
  const [vfCorrectAnswer, setVfCorrectAnswer] = useState<'Verdadeiro' | 'Falso' | ''>(
    question.tipo === 'verdadeiro_falso' ? (question.resposta_correta as 'Verdadeiro' | 'Falso' || '') : ''
  );
  const [essayAnswer, setEssayAnswer] = useState(
    question.tipo === 'dissertativa' ? question.resposta_correta || '' : ''
  );


  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setEditedQuestionData(prev => {
      const updatedData = {
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      };

      if (name === 'tipo') {
        setAlternatives([]);
        setCurrentAlternativeText('');
        setVfCorrectAnswer('');
        setEssayAnswer('');
      }
      return updatedData;
    });
  }

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsArray = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    setEditedQuestionData(prev => ({ ...prev, tags: tagsArray }))
  }

  const handleAddAlternative = () => {
    if (currentAlternativeText.trim() !== '') {
      setAlternatives(prev => [...prev, { text: currentAlternativeText.trim(), is_correct: false }]);
      setCurrentAlternativeText('');
    }
  };

  const handleRemoveAlternative = (indexToRemove: number) => {
    setAlternatives(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleSetCorrectAlternative = (indexToSetCorrect: number) => {
    setAlternatives(prev =>
      prev.map((alt, index) => ({
        ...alt,
        is_correct: index === indexToSetCorrect,
      }))
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    let finalAlternativas: string[] | null = null;
    let finalRespostaCorreta: string = '';

    if (!editedQuestionData.enunciado || !editedQuestionData.disciplina || !editedQuestionData.serie || !editedQuestionData.topico) {
      toast.error('Por favor, preencha os campos obrigatórios: Enunciado, Disciplina, Série e Tópico.');
      return;
    }

    switch (editedQuestionData.tipo) {
      case 'multipla_escolha':
        const correctAlternative = alternatives.find(alt => alt.is_correct);
        if (alternatives.length === 0) {
          toast.error('Para Questões de Múltipla Escolha, adicione pelo menos uma alternativa.');
          return;
        }
        if (!correctAlternative) {
          toast.error('Para Questões de Múltipla Escolha, selecione a alternativa correta.');
          return;
        }
        finalAlternativas = alternatives.map(alt => alt.text);
        finalRespostaCorreta = correctAlternative.text;
        break;
      case 'verdadeiro_falso':
        if (!vfCorrectAnswer) {
          toast.error('Para Questões de Verdadeiro ou Falso, selecione a resposta correta.');
          return;
        }
        finalAlternativas = ['Verdadeiro', 'Falso'];
        finalRespostaCorreta = vfCorrectAnswer;
        break;
      case 'dissertativa':
        if (!essayAnswer.trim()) {
          toast.error('Para Questões Discursivas, preencha a resposta esperada.');
          return;
        }
        finalAlternativas = null;
        finalRespostaCorreta = essayAnswer.trim();
        break;
      default:
        toast.error('Tipo de questão inválido.');
        return;
    }

    const questionToSave: Omit<QuestionUpdate, 'last_modified_at' | 'source'> = {
      ...editedQuestionData,
      alternativas: finalAlternativas,
      resposta_correta: finalRespostaCorreta,
    };

    await onSave(question.id, questionToSave)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fade-in">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl w-full max-w-2xl relative max-h-[90vh] overflow-y-auto"
      >
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Editar Questão</h2>
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="enunciado" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Enunciado da Questão</label>
            <textarea
              id="enunciado"
              name="enunciado"
              value={editedQuestionData.enunciado}
              onChange={handleChange}
              rows={5}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="disciplina" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Disciplina</label>
              <select
                id="disciplina"
                name="disciplina"
                value={editedQuestionData.disciplina}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
              >
                {disciplinasDisponiveis.map(d => <option key={d} value={d}>{d}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="serie" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Série/Nível</label>
              <select
                id="serie"
                name="serie"
                value={editedQuestionData.serie}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
              >
                {seriesDisponiveis.map(s => <option key={s} value={s}>{s}</option>)}
              </select>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="topico" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tópico</label>
              <input
                type="text"
                id="topico"
                name="topico"
                value={editedQuestionData.topico}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label htmlFor="subtopico" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Subtópico (Opcional)</label>
              <input
                type="text"
                id="subtopico"
                name="subtopico"
                value={editedQuestionData.subtopico || ''}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label htmlFor="tipo" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tipo de Questão</label>
              <select
                id="tipo"
                name="tipo"
                value={editedQuestionData.tipo}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              >
                <option value="multipla_escolha">Múltipla Escolha</option>
                <option value="verdadeiro_falso">Verdadeiro ou Falso</option>
                <option value="dissertativa">Discursiva</option>
              </select>
            </div>
            <div>
              <label htmlFor="dificuldade" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Dificuldade</label>
              <select
                id="dificuldade"
                name="dificuldade"
                value={editedQuestionData.dificuldade}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
              >
                <option value="Fácil">Fácil</option>
                <option value="Médio">Médio</option>
                <option value="Difícil">Difícil</option>
              </select>
            </div>
          </div>

          {editedQuestionData.tipo === 'multipla_escolha' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Alternativas</label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  value={currentAlternativeText}
                  onChange={(e) => setCurrentAlternativeText(e.target.value)}
                  placeholder="Nova alternativa"
                  className="flex-grow mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                />
                <button
                  type="button"
                  onClick={handleAddAlternative}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  Adicionar
                </button>
              </div>
              <div className="space-y-2">
                {alternatives.map((alt, index) => (
                  <div key={index} className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700 p-2 rounded-md">
                    <input
                      type="radio"
                      name="correct_alternative"
                      checked={alt.is_correct}
                      onChange={() => handleSetCorrectAlternative(index)}
                      className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-600"
                    />
                    <span className="flex-grow text-gray-900 dark:text-white">{alt.text}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveAlternative(index)}
                      className="p-1 rounded-full text-red-600 hover:bg-red-100 dark:hover:bg-red-700 transition-colors"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {editedQuestionData.tipo === 'verdadeiro_falso' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Resposta Correta (Verdadeiro ou Falso)</label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="vf_correct_answer"
                    value="Verdadeiro"
                    checked={vfCorrectAnswer === 'Verdadeiro'}
                    onChange={() => setVfCorrectAnswer('Verdadeiro')}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="ml-2 text-gray-900 dark:text-white">Verdadeiro</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="vf_correct_answer"
                    value="Falso"
                    checked={vfCorrectAnswer === 'Falso'}
                    onChange={() => setVfCorrectAnswer('Falso')}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <span className="ml-2 text-gray-900 dark:text-white">Falso</span>
                </label>
              </div>
            </div>
          )}

          {editedQuestionData.tipo === 'dissertativa' && (
            <div>
              <label htmlFor="essay_answer_edit" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Resposta Esperada</label>
              <textarea
                id="essay_answer_edit"
                name="essay_answer_edit"
                value={essayAnswer}
                onChange={(e) => setEssayAnswer(e.target.value)}
                rows={5}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
          )}

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Tags (Separadas por vírgula)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={editedQuestionData.tags?.join(', ') || ''}
              onChange={handleTagsChange}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm p-2 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_public_edit"
              name="is_public"
              checked={editedQuestionData.is_public || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_public_edit" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">Disponibilizar publicamente</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_verified_edit"
              name="is_verified"
              checked={editedQuestionData.is_verified || false}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="is_verified_edit" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">Questão Verificada</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Salvando...' : 'Salvar Alterações'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}

interface QuestionViewModalProps {
  question: Question;
  onClose: () => void;
}

const QuestionViewModal: React.FC<QuestionViewModalProps> = ({ question, onClose }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300';
      case 'Médio': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300';
      case 'Difícil': return 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center p-4 z-50 animate-fade-in">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-2xl p-6 relative"
      >
        <button
          onClick={onClose}
          className="absolute top-3 right-3 p-2 rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 transition-colors"
          title="Fechar"
        >
          <X className="w-5 h-5" />
        </button>

        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Detalhes da Questão</h2>

        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <div>
            <p className="font-semibold text-lg mb-1">Enunciado:</p>
            <p className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md whitespace-pre-wrap">{question.enunciado}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Disciplina:</span> {question.disciplina}
            </div>
            <div>
              <span className="font-medium">Série:</span> {question.serie}
            </div>
            <div>
              <span className="font-medium">Tópico:</span> {question.topico}
            </div>
            {question.subtopico && (
              <div>
                <span className="font-medium">Subtópico:</span> {question.subtopico}
              </div>
            )}
            <div>
              <span className="font-medium">Dificuldade:</span>{' '}
              <span className={`px-2 py-0.5 rounded-full text-xs font-semibold ${getDifficultyColor(question.dificuldade)}`}>
                {question.dificuldade}
              </span>
            </div>
            <div>
              <span className="font-medium">Tipo:</span> {question.tipo === 'multipla_escolha' ? 'Múltipla Escolha' : question.tipo === 'verdadeiro_falso' ? 'Verdadeiro ou Falso' : 'Discursiva'}
            </div>
          </div>

          {question.alternativas && question.alternativas.length > 0 && (
            <div>
              <p className="font-medium mb-1">Respostas:</p>
              <ul className="list-disc list-inside p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                {question.alternativas.map((resposta, index) => (
                  <li key={index} className={resposta === question.resposta_correta ? 'font-bold text-green-500' : ''}>
                    {resposta} {resposta === question.resposta_correta && <CheckCircle className="inline-block w-4 h-4 ml-1 text-green-500" />}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {question.tags && question.tags.length > 0 && (
            <div>
              <p className="font-medium mb-1">Tags:</p>
              <div className="flex flex-wrap gap-2">
                {question.tags.map((tag, i) => (
                  <span key={i} className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="flex items-center space-x-4 text-sm">
            <span className={`flex items-center ${question.is_verified ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
              {question.is_verified ? <CheckCircle className="w-4 h-4 mr-1" /> : <AlertTriangle className="w-4 h-4 mr-1" />}
              {question.is_verified ? 'Verificada' : 'Não Verificada'}
            </span>
            <span className={`flex items-center ${question.is_public ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`}>
              {question.is_public ? <Eye className="w-4 h-4 mr-1" /> : <EyeOff className="w-4 h-4 mr-1" />}
              {question.is_public ? 'Pública' : 'Privada'}
            </span>
          </div>

          {question.autor_id && (
            <div className="text-sm">
              <span className="font-medium">Autor ID:</span> {question.autor_id === user?.id ? 'Você' : question.autor_id.substring(0, 8) + '...'}
            </div>
          )}
          {question.created_at && (
            <div className="text-sm">
              <span className="font-medium">Criada em:</span> {new Date(question.created_at).toLocaleString()}
            </div>
          )}
          {question.last_modified_at && (
            <div className="text-sm">
              <span className="font-medium">Última Modificação:</span> {new Date(question.last_modified_at).toLocaleString()}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

const QuestionManagement: React.FC = () => {
  const { user, isAdmin } = useAuth()

  // Estados de filtros
  const [filters, setFilters] = useState({
    search: '',
    disciplina: '',
    serie: '',
    topico: '',
    dificuldade: '',
    tipo: '',
    competencia_bncc: '',
    autor_id: '',
    is_public: undefined,
    is_verified: undefined,
    status: 'all' as 'pending' | 'approved' | 'rejected' | 'all'
  })

  // Estados de paginação com persistência
  const [page, setPage] = useState(() => {
    const saved = localStorage.getItem('questionManagement_page')
    return saved ? parseInt(saved, 10) : 1
  })
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const saved = localStorage.getItem('questionManagement_itemsPerPage')
    return saved ? parseInt(saved, 10) : 10
  })

  // Persistir estado de paginação
  useEffect(() => {
    localStorage.setItem('questionManagement_page', page.toString())
  }, [page])

  useEffect(() => {
    localStorage.setItem('questionManagement_itemsPerPage', itemsPerPage.toString())
  }, [itemsPerPage])

  // Estados de ordenação
  const [orderBy, setOrderBy] = useState<'pending_first' | 'approved_first'>('pending_first')

  // Estados de modais
  const [showCreateQuestionModal, setShowCreateQuestionModal] = useState(false)
  const [showEditQuestionModal, setShowEditQuestionModal] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [showViewQuestionModal, setShowViewQuestionModal] = useState(false)
  const [questionToView, setQuestionToView] = useState<Question | null>(null)

  // Estado para estatísticas
  const [filter, setFilter] = useState<'most_answered' | 'most_wrong'>('most_answered')

  // Memoizar filtros para evitar re-renders desnecessários
  const questionFilters = useMemo(() => ({
    ...filters,
    limit: itemsPerPage,
    page,
  }), [filters, itemsPerPage, page])

  const {
    questions,
    totalCount,
    isLoading,
    error,
    refetch,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    approveQuestion,
    rejectQuestion,
    isCreating,
    isUpdating,
    isDeleting,
    isApproving,
    isRejecting
  } = useQuestions(questionFilters)

  // Handlers para ações das questões
  const handleCreateQuestion = useCallback(async (questionData: Omit<QuestionInsert, 'id' | 'created_at' | 'user_id' | 'last_modified_at' | 'source'>) => {
    try {
      await createQuestion({
        ...questionData,
        autor_id: user?.id,
        source: 'teacher',
        created_at: new Date().toISOString(),
        last_modified_at: new Date().toISOString(),
      })
      setShowCreateQuestionModal(false)
    } catch (error) {
      console.error('Erro ao criar questão:', error)
    }
  }, [createQuestion, user?.id])

  const handleUpdateQuestion = useCallback(async (questionId: string, updates: Omit<QuestionUpdate, 'last_modified_at' | 'source'>) => {
    try {
      const currentQuestion = questions.find(q => q.id === questionId)
      const updatedSource = currentQuestion?.source === 'platform' ? 'platform' : 'teacher'

      await updateQuestion(questionId, {
        ...updates,
        last_modified_at: new Date().toISOString(),
        source: updatedSource
      })
      setShowEditQuestionModal(false)
      setSelectedQuestion(null)
    } catch (error) {
      console.error('Erro ao atualizar questão:', error)
    }
  }, [updateQuestion, questions])

  // Handlers para modais
  const handleOpenCreateQuestionModal = useCallback(() => {
    setShowCreateQuestionModal(true)
  }, [])

  const handleCloseCreateQuestionModal = useCallback(() => {
    setShowCreateQuestionModal(false)
  }, [])

  const handleViewQuestion = useCallback((question: Question) => {
    setQuestionToView(question)
    setShowViewQuestionModal(true)
  }, [])

  const handleCloseViewQuestionModal = useCallback(() => {
    setQuestionToView(null)
    setShowViewQuestionModal(false)
  }, [])

  const handleOpenEditQuestionModal = useCallback((question: Question) => {
    setSelectedQuestion(question)
    setShowEditQuestionModal(true)
  }, [])

  const handleCloseEditQuestionModal = useCallback(() => {
    setSelectedQuestion(null)
    setShowEditQuestionModal(false)
  }, [])

  const handleApproveQuestion = useCallback(async (questionId: string) => {
    if (window.confirm('Tem certeza que deseja APROVAR esta questão?')) {
      try {
        await approveQuestion(questionId)
      } catch (error) {
        console.error('Erro ao aprovar questão:', error)
      }
    }
  }, [approveQuestion])

  const handleRejectQuestion = useCallback(async (questionId: string) => {
    if (window.confirm('Tem certeza que deseja REJEITAR esta questão?')) {
      try {
        await rejectQuestion(questionId)
      } catch (error) {
        console.error('Erro ao rejeitar questão:', error)
      }
    }
  }, [rejectQuestion])

  const handleDeleteQuestion = useCallback(async (questionId: string) => {
    if (!window.confirm('Tem certeza que deseja excluir esta questão? Esta ação não pode ser desfeita.')) {
      return
    }
    try {
      await deleteQuestion(questionId)
    } catch (error) {
      console.error('Erro ao excluir questão:', error)
    }
  }, [deleteQuestion])

  // Aplicar ordenação local apenas se necessário (a ordenação principal deve vir do servidor)
  const orderedQuestions = useMemo(() => {
    if (!questions.length) return []

    return [...questions].sort((a, b) => {
      if (orderBy === 'pending_first') {
        if (a.status === 'pending' && b.status !== 'pending') return -1
        if (a.status !== 'pending' && b.status === 'pending') return 1
        return 0
      } else {
        if (a.status === 'approved' && b.status !== 'approved') return -1
        if (a.status !== 'approved' && b.status === 'approved') return 1
        return 0
      }
    })
  }, [questions, orderBy])

  // Calcular informações de paginação
  const totalPages = Math.ceil(totalCount / itemsPerPage)
  const startItem = (page - 1) * itemsPerPage + 1
  const endItem = Math.min(page * itemsPerPage, totalCount)

  // Handlers de paginação
  const handlePageChange = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== page) {
      setPage(newPage)
      // Scroll para o topo da tabela
      document.querySelector('.bg-white.dark\\:bg-gray-800.rounded-xl')?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }, [totalPages, page])

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    if (newItemsPerPage !== itemsPerPage) {
      setItemsPerPage(newItemsPerPage)
      setPage(1) // Reset para primeira página
    }
  }, [itemsPerPage])

  const handleFilterChange = useCallback((filterName: string, value: any) => {
    setFilters(prev => {
      if (prev[filterName as keyof typeof prev] !== value) {
        setPage(1) // Reset para primeira página quando filtros mudam
        return { ...prev, [filterName]: value }
      }
      return prev
    })
  }, [])

  const { data: topQuestions, isLoading: topQuestionsLoading } = useQuery({
    queryKey: ['topQuestions', filter],
    queryFn: async () => {
      let query = supabase.from('questions').select('id, enunciado, disciplina, uso_count, rating, rating_count')
      if (filter === 'most_answered') {
        query = query.order('uso_count', { ascending: false })
      } else {
        query = query.order('rating_count', { ascending: false })
      }
      const { data } = await query.limit(10)
      return data || []
    }
  })

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader className="w-8 h-8 text-red-600 dark:text-red-500 animate-spin mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Carregando questões...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center text-red-600 dark:text-red-400">
            <AlertTriangle className="w-8 h-8 mx-auto mb-4" />
            <p>Erro ao carregar questões: {error.message}</p>
            <button
              onClick={() => refetch?.()}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
              aria-label="Tentar carregar questões novamente"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="p-6 max-w-7xl mx-auto"
    >
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Questões
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {totalCount > 0 ? (
              <>
                Mostrando {startItem}-{endItem} de {totalCount} questões
                {totalPages > 1 && ` (Página ${page} de ${totalPages})`}
              </>
            ) : (
              'Nenhuma questão encontrada'
            )}
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleOpenCreateQuestionModal}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5 mr-2" /> Criar Nova Questão
          </button>
          <button
            onClick={() => refetch?.()}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50"
            disabled={isLoading}
            aria-label="Atualizar lista de questões"
          >
            <RefreshCw className={`w-5 h-5 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Atualizando...' : 'Atualizar Lista'}
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar por enunciado, disciplina, tópico..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              aria-label="Buscar questões"
            />
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              className="w-full md:w-48 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
              value={filters.disciplina}
              onChange={(e) => handleFilterChange('disciplina', e.target.value)}
              aria-label="Filtrar por disciplina"
            >
              <option value="">Todas as disciplinas</option>
              {disciplinasDisponiveis.map(d => <option key={d} value={d}>{d}</option>)}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828l-3.243-3.242L5.343 8z"/></svg>
            </div>
          </div>
          <div className="relative">
            <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              className="w-full md:w-48 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
              value={filters.serie}
              onChange={(e) => handleFilterChange('serie', e.target.value)}
              aria-label="Filtrar por série"
            >
              <option value="">Todas as séries</option>
              {seriesDisponiveis.map(s => <option key={s} value={s}>{s}</option>)}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828l-3.243-3.242L5.343 8z"/></svg>
            </div>
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              className="w-full md:w-48 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value as 'pending' | 'approved' | 'rejected' | 'all')}
              aria-label="Filtrar por status"
            >
              <option value="all">Todos os status</option>
              <option value="pending">Pendente</option>
              <option value="approved">Aprovada</option>
              <option value="rejected">Rejeitada</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828l-3.243-3.242L5.343 8z"/></svg>
            </div>
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              className="w-full md:w-48 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none"
              value={orderBy}
              onChange={e => setOrderBy(e.target.value as 'pending_first' | 'approved_first')}
              aria-label="Ordenar questões"
            >
              <option value="pending_first">Pendentes primeiro</option>
              <option value="approved_first">Aprovadas primeiro</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828l-3.243-3.242L5.343 8z"/></svg>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end mb-4">
        <button
          onClick={handleOpenCreateQuestionModal}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          <Plus className="h-5 w-5 mr-2" />
          Nova Questão
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Loader className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-700 dark:text-gray-300">Carregando questões...</span>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Enunciado
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Detalhes
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {orderedQuestions.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                    Nenhuma questão encontrada com os filtros aplicados.
                  </td>
                </tr>
              ) : (
                orderedQuestions.map((question) => (
                  <tr key={question.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 max-w-xs truncate text-sm font-medium text-gray-900 dark:text-white">
                      {question.enunciado}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <p><strong>Disciplina:</strong> {question.disciplina}</p>
                      <p><strong>Série:</strong> {question.serie}</p>
                      <p><strong>Tópico:</strong> {question.topico}</p>
                      <p><strong>Dificuldade:</strong> {question.dificuldade}</p>
                      <p><strong>Tipo:</strong> {question.tipo}</p>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        question.status === 'pending' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                        question.status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {question.status === 'pending' ? 'Pendente' :
                         question.status === 'approved' ? 'Aprovada' :
                         'Rejeitada'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewQuestion(question)}
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                          title="Visualizar"
                        >
                          <Eye className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleOpenEditQuestionModal(question)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          title="Editar"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        {isAdmin && question.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleApproveQuestion(question.id)}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Aprovar"
                            >
                              <CheckCircle className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleRejectQuestion(question.id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              title="Rejeitar"
                            >
                              <XCircle className="h-5 w-5" />
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => handleDeleteQuestion(question.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Excluir"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Controles de Paginação */}
        {totalCount > 0 && (
          <div className="bg-white dark:bg-gray-800 px-6 py-4 border-t border-gray-200 dark:border-gray-700 rounded-b-xl">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              {/* Seletor de itens por página */}
              <div className="flex items-center gap-2">
                <label htmlFor="items-per-page" className="text-sm text-gray-700 dark:text-gray-300">
                  Itens por página:
                </label>
                <select
                  id="items-per-page"
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  aria-label="Selecionar quantidade de itens por página"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>

              {/* Informações de paginação */}
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Mostrando {startItem}-{endItem} de {totalCount} questões
              </div>

              {/* Controles de navegação */}
              <div className="flex items-center gap-1">
                {/* Primeira página */}
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={page === 1}
                  className="p-2 rounded-md border border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Primeira página"
                >
                  <ChevronsLeft className="w-4 h-4" />
                </button>

                {/* Página anterior */}
                <button
                  onClick={() => handlePageChange(page - 1)}
                  disabled={page === 1}
                  className="p-2 rounded-md border border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Página anterior"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>

                {/* Números das páginas */}
                <div className="flex items-center gap-1">
                  {totalPages > 5 && page > 3 && (
                    <>
                      <button
                        onClick={() => handlePageChange(1)}
                        className="px-3 py-1 rounded-md text-sm font-medium border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        aria-label="Página 1"
                      >
                        1
                      </button>
                      {page > 4 && (
                        <span className="px-2 text-gray-500 dark:text-gray-400">...</span>
                      )}
                    </>
                  )}

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber
                    if (totalPages <= 5) {
                      pageNumber = i + 1
                    } else if (page <= 3) {
                      pageNumber = i + 1
                    } else if (page >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i
                    } else {
                      pageNumber = page - 2 + i
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                          page === pageNumber
                            ? 'bg-blue-600 text-white'
                            : 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                        aria-label={`Página ${pageNumber}`}
                        aria-current={page === pageNumber ? 'page' : undefined}
                      >
                        {pageNumber}
                      </button>
                    )
                  })}

                  {totalPages > 5 && page < totalPages - 2 && (
                    <>
                      {page < totalPages - 3 && (
                        <span className="px-2 text-gray-500 dark:text-gray-400">...</span>
                      )}
                      <button
                        onClick={() => handlePageChange(totalPages)}
                        className="px-3 py-1 rounded-md text-sm font-medium border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        aria-label={`Página ${totalPages}`}
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>

                {/* Próxima página */}
                <button
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page === totalPages}
                  className="p-2 rounded-md border border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Próxima página"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>

                {/* Última página */}
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={page === totalPages}
                  className="p-2 rounded-md border border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  aria-label="Última página"
                >
                  <ChevronsRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}

      {showCreateQuestionModal && (
        <QuestionCreateModal
          onClose={handleCloseCreateQuestionModal}
          onSave={handleCreateQuestion}
          isLoading={isCreating}
        />
      )}

      {selectedQuestion && (
        <QuestionEditModal
          onClose={handleCloseEditQuestionModal}
          onSave={handleUpdateQuestion}
          isLoading={isUpdating}
          question={selectedQuestion}
        />
      )}

      {questionToView && (
        <QuestionDetailModal
          isOpen={showViewQuestionModal}
          onClose={handleCloseViewQuestionModal}
          question={questionToView}
          isAdmin={isAdmin}
          onApproveQuestion={handleApproveQuestion}
          onRejectQuestion={handleRejectQuestion}
          isApproving={isApproving}
          isRejecting={isRejecting}
        />
      )}

      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Questões Mais Respondidas/Erradas</h1>
          <div className="flex space-x-2">
            <button onClick={() => setFilter('most_answered')} className={`px-3 py-1 rounded ${filter==='most_answered'?'bg-blue-600 text-white':'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Mais respondidas</button>
            <button onClick={() => setFilter('most_wrong')} className={`px-3 py-1 rounded ${filter==='most_wrong'?'bg-blue-600 text-white':'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200'}`}>Mais erradas</button>
          </div>
        </div>
        {topQuestionsLoading ? (
          <div className="flex justify-center items-center h-40"><Loader className="w-8 h-8 animate-spin" /></div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {topQuestions?.map((q: any) => (
              <div key={q.id} className="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{q.enunciado}</h2>
                <p className="text-xs text-gray-500 mb-1">Disciplina: {q.disciplina || 'N/A'}</p>
                <p className="text-xs text-gray-400 mb-1">Respostas: {q.respostas || 0}</p>
                <p className="text-xs text-red-500 mb-2">Erros: {q.erros || 0}</p>
                <button className="mt-auto flex items-center space-x-2 text-blue-600 hover:underline"><Eye className="w-4 h-4" /><span>Ver detalhes</span></button>
              </div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default QuestionManagement