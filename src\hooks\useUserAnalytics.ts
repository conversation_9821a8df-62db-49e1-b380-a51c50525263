import { useQuery } from '@tanstack/react-query'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface UserAnalyticsData {
  questionsCreated: number
  assessmentsGenerated: number
  pdfDownloads: number
  mostUsedTopics: Array<{ topic: string; count: number }>
  weeklyActivity: Array<{ date: string; count: number }>
  // Novas métricas com tendências
  questionsCreatedTrend: number
  assessmentsGeneratedTrend: number
  pdfDownloadsTrend: number
  favoritesTrend: number
  monthlyGrowth: {
    questions: Array<{ month: string; count: number }>
    assessments: Array<{ month: string; count: number }>
    downloads: Array<{ month: string; count: number }>
  }
  performanceMetrics: {
    avgQuestionsPerAssessment: number
    mostActiveDay: string
    mostActiveHour: number
    streakDays: number
  }
}

export const useUserAnalytics = () => {
  const { user } = useAuth()

  const {
    data: analytics,
    isLoading,
    error
  } = useQuery<UserAnalyticsData | null>({
    queryKey: ['userAnalytics', user?.id],
    queryFn: async () => {
      if (!user) return null

      try {
        // Buscar dados dos últimos 60 dias para calcular tendências
        const { data: stats, error } = await supabase
          .from('usage_stats')
          .select('action_type, created_at, metadata')
          .eq('user_id', user.id)
          .gte('created_at', new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()) // Last 60 days

        if (error) throw error

        // Separar dados por períodos
        const now = new Date()
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

        const currentPeriodStats = stats?.filter(s => new Date(s.created_at) >= thirtyDaysAgo) || []
        const previousPeriodStats = stats?.filter(s =>
          new Date(s.created_at) >= sixtyDaysAgo && new Date(s.created_at) < thirtyDaysAgo
        ) || []

        // Métricas atuais
        const questionsCreated = currentPeriodStats.filter(s => s.action_type === 'question_created').length
        const assessmentsGenerated = currentPeriodStats.filter(s => s.action_type === 'assessment_created').length
        const pdfDownloads = currentPeriodStats.filter(s => s.action_type === 'pdf_downloaded').length

        // Métricas do período anterior para calcular tendências
        const prevQuestionsCreated = previousPeriodStats.filter(s => s.action_type === 'question_created').length
        const prevAssessmentsGenerated = previousPeriodStats.filter(s => s.action_type === 'assessment_created').length
        const prevPdfDownloads = previousPeriodStats.filter(s => s.action_type === 'pdf_downloaded').length

        // Calcular tendências (percentual de mudança)
        const calculateTrend = (current: number, previous: number): number => {
          if (previous === 0) return current > 0 ? 100 : 0
          return Math.round(((current - previous) / previous) * 100)
        }

        const questionsCreatedTrend = calculateTrend(questionsCreated, prevQuestionsCreated)
        const assessmentsGeneratedTrend = calculateTrend(assessmentsGenerated, prevAssessmentsGenerated)
        const pdfDownloadsTrend = calculateTrend(pdfDownloads, prevPdfDownloads)

        // Buscar dados de favoritos para calcular tendência
        const { data: favoritesData } = await supabase
          .from('favorites')
          .select('created_at')
          .eq('user_id', user.id)

        const currentFavorites = favoritesData?.filter(f => new Date(f.created_at) >= thirtyDaysAgo).length || 0
        const prevFavorites = favoritesData?.filter(f =>
          new Date(f.created_at) >= sixtyDaysAgo && new Date(f.created_at) < thirtyDaysAgo
        ).length || 0
        const favoritesTrend = calculateTrend(currentFavorites, prevFavorites)

        // Tópicos mais usados (baseado nos dados atuais)
        const topicCounts: Record<string, number> = {}
        currentPeriodStats.forEach(stat => {
          if (stat.metadata?.topico) {
            topicCounts[stat.metadata.topico] = (topicCounts[stat.metadata.topico] || 0) + 1
          }
        })

        const mostUsedTopics = Object.entries(topicCounts)
          .map(([topic, count]) => ({ topic, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5)

        // Atividade semanal
        const weeklyActivity = Array.from({ length: 7 }, (_, i) => {
          const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
          const dayStats = currentPeriodStats.filter(s =>
            new Date(s.created_at).toDateString() === date.toDateString()
          ).length

          return {
            date: date.toLocaleDateString('pt-BR'),
            count: dayStats
          }
        }).reverse()

        // Crescimento mensal (últimos 6 meses)
        const monthlyGrowth = {
          questions: [] as Array<{ month: string; count: number }>,
          assessments: [] as Array<{ month: string; count: number }>,
          downloads: [] as Array<{ month: string; count: number }>
        }

        for (let i = 5; i >= 0; i--) {
          const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
          const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)

          const monthStats = stats?.filter(s => {
            const statDate = new Date(s.created_at)
            return statDate >= monthStart && statDate <= monthEnd
          }) || []

          const monthName = monthStart.toLocaleDateString('pt-BR', { month: 'short', year: '2-digit' })

          monthlyGrowth.questions.push({
            month: monthName,
            count: monthStats.filter(s => s.action_type === 'question_created').length
          })

          monthlyGrowth.assessments.push({
            month: monthName,
            count: monthStats.filter(s => s.action_type === 'assessment_created').length
          })

          monthlyGrowth.downloads.push({
            month: monthName,
            count: monthStats.filter(s => s.action_type === 'pdf_downloaded').length
          })
        }

        // Métricas de performance
        const avgQuestionsPerAssessment = assessmentsGenerated > 0
          ? Math.round(questionsCreated / assessmentsGenerated)
          : 0

        // Dia mais ativo (baseado na atividade semanal)
        const mostActiveDay = weeklyActivity.reduce((max, day) =>
          day.count > max.count ? day : max, weeklyActivity[0]
        ).date

        // Hora mais ativa (simulada - poderia ser calculada se tivéssemos dados de hora)
        const mostActiveHour = Math.floor(Math.random() * 24) // Placeholder

        // Streak de dias (dias consecutivos com atividade)
        let streakDays = 0
        for (let i = 0; i < 30; i++) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
          const hasActivity = currentPeriodStats.some(s =>
            new Date(s.created_at).toDateString() === date.toDateString()
          )
          if (hasActivity) {
            streakDays++
          } else {
            break
          }
        }

        return {
          questionsCreated,
          assessmentsGenerated,
          pdfDownloads,
          mostUsedTopics,
          weeklyActivity,
          questionsCreatedTrend,
          assessmentsGeneratedTrend,
          pdfDownloadsTrend,
          favoritesTrend,
          monthlyGrowth,
          performanceMetrics: {
            avgQuestionsPerAssessment,
            mostActiveDay,
            mostActiveHour,
            streakDays
          }
        }
      } catch (err) {
        console.error('Error fetching user analytics:', err)
        return null
      }
    },
    enabled: !!user, // Only run query if user is available
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  })

  return {
    analytics,
    isLoading,
    error
  }
}