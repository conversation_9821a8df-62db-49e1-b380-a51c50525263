import React, { useState, useEffect } from 'react'
import { X, Plus, Trash2, Save, Database } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import { DISCIPLINAS, SERIES } from '../../constants/educationOptions'
import { Database as DatabaseType } from '../../types/database'
import toast from 'react-hot-toast'

type Question = DatabaseType['public']['Tables']['questions']['Row']
type QuestionInsert = DatabaseType['public']['Tables']['questions']['Insert']

interface CustomQuestion {
  id: string
  enunciado: string
  tipo: 'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'
  alternativas?: string[]
  resposta_correta: string
  linhas_resposta?: number
  disciplina?: string
  serie?: string
  dificuldade?: 'Fácil' | 'Médio' | 'Difícil'
  topico?: string
  tags?: string[]
}

interface CustomQuestionModalProps {
  isOpen: boolean
  onClose: () => void
  onInsertOnly: (question: CustomQuestion) => void
  onInsertAndSave: (question: CustomQuestion, questionData: QuestionInsert) => void
}

const CustomQuestionModal: React.FC<CustomQuestionModalProps> = ({
  isOpen,
  onClose,
  onInsertOnly,
  onInsertAndSave
}) => {
  const { user } = useAuth()
  
  // Estados do formulário
  const [enunciado, setEnunciado] = useState('')
  const [tipo, setTipo] = useState<'multipla_escolha' | 'dissertativa' | 'verdadeiro_falso'>('multipla_escolha')
  const [alternativas, setAlternativas] = useState(['', ''])
  const [respostaCorreta, setRespostaCorreta] = useState('')
  const [linhasResposta, setLinhasResposta] = useState(3)
  const [saveToDatabase, setSaveToDatabase] = useState(false)
  
  // Campos adicionais para salvar no banco
  const [disciplina, setDisciplina] = useState('')
  const [serie, setSerie] = useState('')
  const [dificuldade, setDificuldade] = useState<'Fácil' | 'Médio' | 'Difícil'>('Médio')
  const [topico, setTopico] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      resetForm()
    }
  }, [isOpen])

  const resetForm = () => {
    setEnunciado('')
    setTipo('multipla_escolha')
    setAlternativas(['', ''])
    setRespostaCorreta('')
    setLinhasResposta(3)
    setSaveToDatabase(false)
    setDisciplina('')
    setSerie('')
    setDificuldade('Médio')
    setTopico('')
    setTags([])
    setNewTag('')
  }

  const addAlternativa = () => {
    if (alternativas.length < 5) {
      setAlternativas([...alternativas, ''])
    }
  }

  const removeAlternativa = (index: number) => {
    if (alternativas.length > 2) {
      const newAlternativas = alternativas.filter((_, i) => i !== index)
      setAlternativas(newAlternativas)
      // Se a resposta correta era a alternativa removida, limpar
      if (respostaCorreta === alternativas[index]) {
        setRespostaCorreta('')
      }
    }
  }

  const updateAlternativa = (index: number, value: string) => {
    const newAlternativas = [...alternativas]
    newAlternativas[index] = value
    setAlternativas(newAlternativas)
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index))
  }

  const validateForm = (): string | null => {
    if (!enunciado.trim()) {
      return 'Enunciado é obrigatório'
    }

    if (tipo === 'multipla_escolha') {
      const validAlternativas = alternativas.filter(alt => alt.trim())
      if (validAlternativas.length < 2) {
        return 'Pelo menos 2 alternativas são obrigatórias'
      }
      if (!respostaCorreta) {
        return 'Selecione a resposta correta'
      }
    }

    if (tipo === 'verdadeiro_falso' && !respostaCorreta) {
      return 'Selecione a resposta correta (Verdadeiro ou Falso)'
    }

    if (saveToDatabase) {
      if (!disciplina) return 'Disciplina é obrigatória para salvar no banco'
      if (!serie) return 'Série é obrigatória para salvar no banco'
      if (!topico.trim()) return 'Tópico é obrigatório para salvar no banco'
    }

    return null
  }

  const handleSubmit = () => {
    const error = validateForm()
    if (error) {
      toast.error(error)
      return
    }

    const customQuestion: CustomQuestion = {
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      enunciado: enunciado.trim(),
      tipo,
      resposta_correta: respostaCorreta,
      ...(tipo === 'multipla_escolha' && { 
        alternativas: alternativas.filter(alt => alt.trim()) 
      }),
      ...(tipo === 'dissertativa' && { linhas_resposta: linhasResposta }),
      ...(saveToDatabase && {
        disciplina,
        serie,
        dificuldade,
        topico: topico.trim(),
        tags
      })
    }

    if (saveToDatabase) {
      const questionData: QuestionInsert = {
        disciplina,
        serie,
        topico: topico.trim(),
        subtopico: null,
        dificuldade,
        tipo,
        competencia_bncc: null,
        enunciado: enunciado.trim(),
        alternativas: tipo === 'multipla_escolha' ? alternativas.filter(alt => alt.trim()) : null,
        resposta_correta: respostaCorreta,
        explicacao: 'Questão criada pelo professor',
        imagem_url: null,
        tags,
        autor_id: user?.id || '',
        uso_count: 0,
        rating: 0,
        rating_count: 0,
        is_public: false,
        is_verified: false,
        metadata: {},
        status: 'approved',
        is_shared_with_school: false,
        school_id: null
      }
      onInsertAndSave(customQuestion, questionData)
    } else {
      onInsertOnly(customQuestion)
    }

    onClose()
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Criar Nova Questão
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Enunciado */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Enunciado da Questão *
              </label>
              <textarea
                value={enunciado}
                onChange={(e) => setEnunciado(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={3}
                placeholder="Digite o enunciado da questão..."
              />
            </div>

            {/* Tipo de Questão */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tipo de Questão *
              </label>
              <select
                value={tipo}
                onChange={(e) => setTipo(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="multipla_escolha">Múltipla Escolha</option>
                <option value="dissertativa">Discursiva</option>
                <option value="verdadeiro_falso">Verdadeiro ou Falso</option>
              </select>
            </div>

            {/* Campos condicionais baseados no tipo */}
            {tipo === 'multipla_escolha' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Alternativas *
                </label>
                <div className="space-y-3">
                  {alternativas.map((alternativa, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="resposta_correta"
                        value={alternativa}
                        checked={respostaCorreta === alternativa}
                        onChange={(e) => setRespostaCorreta(e.target.value)}
                        className="text-blue-600"
                      />
                      <input
                        type="text"
                        value={alternativa}
                        onChange={(e) => updateAlternativa(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder={`Alternativa ${String.fromCharCode(65 + index)}`}
                      />
                      {alternativas.length > 2 && (
                        <button
                          onClick={() => removeAlternativa(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  {alternativas.length < 5 && (
                    <button
                      onClick={addAlternativa}
                      className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Adicionar Alternativa</span>
                    </button>
                  )}
                </div>
              </div>
            )}

            {tipo === 'dissertativa' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quantidade de Linhas para Resposta
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={linhasResposta}
                  onChange={(e) => setLinhasResposta(parseInt(e.target.value) || 3)}
                  className="w-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            )}

            {tipo === 'verdadeiro_falso' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Resposta Correta *
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="vf_resposta"
                      value="Verdadeiro"
                      checked={respostaCorreta === 'Verdadeiro'}
                      onChange={(e) => setRespostaCorreta(e.target.value)}
                      className="text-blue-600 mr-2"
                    />
                    Verdadeiro
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="vf_resposta"
                      value="Falso"
                      checked={respostaCorreta === 'Falso'}
                      onChange={(e) => setRespostaCorreta(e.target.value)}
                      className="text-blue-600 mr-2"
                    />
                    Falso
                  </label>
                </div>
              </div>
            )}

            {/* Opção de salvar no banco */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={saveToDatabase}
                  onChange={(e) => setSaveToDatabase(e.target.checked)}
                  className="text-blue-600"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Salvar questão no meu banco pessoal
                </span>
              </label>
            </div>

            {/* Campos adicionais quando salvar no banco está marcado */}
            {saveToDatabase && (
              <div className="space-y-4 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Disciplina *
                    </label>
                    <select
                      value={disciplina}
                      onChange={(e) => setDisciplina(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Selecione uma disciplina</option>
                      {DISCIPLINAS.map(disc => (
                        <option key={disc} value={disc}>{disc}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Série *
                    </label>
                    <select
                      value={serie}
                      onChange={(e) => setSerie(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Selecione uma série</option>
                      {SERIES.map(ser => (
                        <option key={ser} value={ser}>{ser}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Dificuldade *
                    </label>
                    <select
                      value={dificuldade}
                      onChange={(e) => setDificuldade(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="Fácil">Fácil</option>
                      <option value="Médio">Médio</option>
                      <option value="Difícil">Difícil</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tópico *
                    </label>
                    <input
                      type="text"
                      value={topico}
                      onChange={(e) => setTopico(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="Ex: Frações, Revolução Industrial..."
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags (opcional)
                  </label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-sm flex items-center"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(index)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="Digite uma tag e pressione Enter"
                    />
                    <button
                      onClick={addTag}
                      className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Botões de ação */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancelar
            </button>
            <button
              onClick={handleSubmit}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              {saveToDatabase ? <Database className="w-4 h-4" /> : <Save className="w-4 h-4" />}
              <span>
                {saveToDatabase ? 'Inserir e Salvar no Banco' : 'Inserir Questão'}
              </span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default CustomQuestionModal
