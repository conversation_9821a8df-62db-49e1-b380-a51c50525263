import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
// import { initializePerformanceSystem } from './lib/performance/init';

// DESATIVADO: Inicializar sistema de performance
// initializePerformanceSystem().catch(error => {
//   console.error('Failed to initialize performance system:', error);
// });

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);